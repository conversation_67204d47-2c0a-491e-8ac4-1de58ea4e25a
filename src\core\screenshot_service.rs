// src/core/screenshot_service.rs
use std::sync::{Arc, Mutex};
use std::thread;
use std::time::Duration;
use winapi::um::winbase::{CreateNamedPipeA, ConnectNamedPipe, DisconnectNamedPipe, PIPE_ACCESS_DUPLEX, PIPE_TYPE_BYTE, PIPE_READMODE_BYTE, PIPE_WAIT};
use winapi::um::fileapi::{ReadFile, WriteFile};
use winapi::um::handleapi::{CloseHandle, INVALID_HANDLE_VALUE};
use winapi::shared::minwindef::{DWORD, FALSE};
use winapi::shared::ntdef::HANDLE;
use log::{info, error, debug, warn};
use serde::{Serialize, Deserialize};

use crate::core::screenshot::ScreenshotManager;
use crate::error::BypassError;

/// 截图请求结构
#[derive(Serialize, Deserialize, Debug)]
pub struct ScreenshotRequest {
    pub format: ImageFormat,
    pub quality: u8,        // 1-100 for JPEG
    pub monitor: u32,       // 0 = primary, 1,2,3... = specific monitor
    pub max_width: u32,     // 最大宽度，0 = 不限制
    pub max_height: u32,    // 最大高度，0 = 不限制
}

/// 截图响应结构
#[derive(Serialize, Deserialize, Debug)]
pub struct ScreenshotResponse {
    pub success: bool,
    pub data: Vec<u8>,
    pub width: u32,
    pub height: u32,
    pub format: ImageFormat,
    pub error_message: Option<String>,
}

/// 支持的图像格式
#[derive(Serialize, Deserialize, Debug, Clone)]
pub enum ImageFormat {
    JPEG,
    PNG,
    BMP,
}

/// 截图服务配置
#[derive(Debug, Clone)]
pub struct ScreenshotServiceConfig {
    pub pipe_name: String,
    pub timeout_ms: u32,
    pub max_clients: u32,
    pub max_image_size_mb: u32,
    pub default_quality: u8,
}

impl Default for ScreenshotServiceConfig {
    fn default() -> Self {
        Self {
            pipe_name: "\\\\.\\pipe\\rust_screenshot_pipe".to_string(),
            timeout_ms: 10000,
            max_clients: 5,
            max_image_size_mb: 50,
            default_quality: 85,
        }
    }
}

/// 截图服务主结构
pub struct ScreenshotService {
    config: ScreenshotServiceConfig,
    screenshot_manager: Arc<Mutex<ScreenshotManager>>,
    running: Arc<Mutex<bool>>,
}

impl ScreenshotService {
    /// 创建新的截图服务实例
    pub fn new(config: ScreenshotServiceConfig, screenshot_manager: Arc<Mutex<ScreenshotManager>>) -> Self {
        Self {
            config,
            screenshot_manager,
            running: Arc::new(Mutex::new(false)),
        }
    }

    /// 启动截图服务
    pub fn start(&self) -> Result<(), BypassError> {
        info!("Starting screenshot service on pipe: {}", self.config.pipe_name);
        
        // 设置运行状态
        {
            let mut running = self.running.lock().unwrap();
            *running = true;
        }

        // 创建服务线程
        let config = self.config.clone();
        let screenshot_manager = self.screenshot_manager.clone();
        let running = self.running.clone();

        thread::spawn(move || {
            Self::service_loop(config, screenshot_manager, running);
        });

        info!("Screenshot service started successfully");
        Ok(())
    }

    /// 停止截图服务
    pub fn stop(&self) {
        info!("Stopping screenshot service...");
        let mut running = self.running.lock().unwrap();
        *running = false;
    }

    /// 服务主循环
    fn service_loop(
        config: ScreenshotServiceConfig,
        screenshot_manager: Arc<Mutex<ScreenshotManager>>,
        running: Arc<Mutex<bool>>,
    ) {
        while *running.lock().unwrap() {
            // 创建命名管道
            let pipe_handle = unsafe {
                CreateNamedPipeA(
                    config.pipe_name.as_ptr() as *const i8,
                    PIPE_ACCESS_DUPLEX,
                    PIPE_TYPE_BYTE | PIPE_READMODE_BYTE | PIPE_WAIT,
                    config.max_clients,
                    8192,  // 输出缓冲区大小
                    8192,  // 输入缓冲区大小
                    config.timeout_ms,
                    std::ptr::null_mut(),
                )
            };

            if pipe_handle == INVALID_HANDLE_VALUE {
                error!("Failed to create named pipe: {}", unsafe { winapi::um::errhandlingapi::GetLastError() });
                thread::sleep(Duration::from_secs(1));
                continue;
            }

            debug!("Named pipe created, waiting for client connection...");

            // 等待客户端连接
            let connected = unsafe { ConnectNamedPipe(pipe_handle, std::ptr::null_mut()) };
            if connected == FALSE {
                let error_code = unsafe { winapi::um::errhandlingapi::GetLastError() };
                if error_code != winapi::shared::winerror::ERROR_PIPE_CONNECTED {
                    error!("Failed to connect to client: {}", error_code);
                    unsafe { CloseHandle(pipe_handle) };
                    continue;
                }
            }

            info!("Client connected to screenshot service");

            // 处理客户端请求
            Self::handle_client(pipe_handle, &screenshot_manager, &config);

            // 断开连接并关闭管道
            unsafe {
                DisconnectNamedPipe(pipe_handle);
                CloseHandle(pipe_handle);
            }

            debug!("Client disconnected");
        }

        info!("Screenshot service loop ended");
    }

    /// 处理单个客户端请求
    fn handle_client(
        pipe_handle: HANDLE,
        screenshot_manager: &Arc<Mutex<ScreenshotManager>>,
        config: &ScreenshotServiceConfig,
    ) {
        // 读取请求数据
        let request = match Self::read_request(pipe_handle) {
            Ok(req) => req,
            Err(e) => {
                error!("Failed to read request: {:?}", e);
                Self::send_error_response(pipe_handle, "Failed to read request");
                return;
            }
        };

        debug!("Received screenshot request: {:?}", request);

        // 执行截图
        let response = Self::process_screenshot_request(request, screenshot_manager, config);

        // 发送响应
        if let Err(e) = Self::send_response(pipe_handle, &response) {
            error!("Failed to send response: {:?}", e);
        }
    }

    /// 读取客户端请求
    fn read_request(pipe_handle: HANDLE) -> Result<ScreenshotRequest, BypassError> {
        let mut buffer = [0u8; 1024];
        let mut bytes_read: DWORD = 0;

        let success = unsafe {
            ReadFile(
                pipe_handle,
                buffer.as_mut_ptr() as *mut winapi::ctypes::c_void,
                buffer.len() as DWORD,
                &mut bytes_read,
                std::ptr::null_mut(),
            )
        };

        if success == FALSE {
            return Err(BypassError::OperationFailed(format!(
                "Failed to read from pipe: {}",
                unsafe { winapi::um::errhandlingapi::GetLastError() }
            )));
        }

        let request_data = &buffer[..bytes_read as usize];
        let request: ScreenshotRequest = serde_json::from_slice(request_data)
            .map_err(|e| BypassError::OperationFailed(format!("Failed to parse request: {}", e)))?;

        Ok(request)
    }

    /// 处理截图请求
    fn process_screenshot_request(
        request: ScreenshotRequest,
        screenshot_manager: &Arc<Mutex<ScreenshotManager>>,
        config: &ScreenshotServiceConfig,
    ) -> ScreenshotResponse {
        let mut manager = match screenshot_manager.lock() {
            Ok(mgr) => mgr,
            Err(e) => {
                error!("Failed to lock screenshot manager: {}", e);
                return ScreenshotResponse {
                    success: false,
                    data: Vec::new(),
                    width: 0,
                    height: 0,
                    format: request.format,
                    error_message: Some("Internal error: failed to access screenshot manager".to_string()),
                };
            }
        };

        // 执行截图
        match manager.capture_screen_with_options(&request) {
            Ok((data, width, height)) => {
                // 检查文件大小限制
                let size_mb = data.len() as f64 / (1024.0 * 1024.0);
                if size_mb > config.max_image_size_mb as f64 {
                    warn!("Screenshot size ({:.2}MB) exceeds limit ({}MB)", size_mb, config.max_image_size_mb);
                    return ScreenshotResponse {
                        success: false,
                        data: Vec::new(),
                        width: 0,
                        height: 0,
                        format: request.format,
                        error_message: Some(format!("Screenshot too large: {:.2}MB", size_mb)),
                    };
                }

                info!("Screenshot captured successfully: {}x{}, {:.2}MB", width, height, size_mb);
                ScreenshotResponse {
                    success: true,
                    data,
                    width,
                    height,
                    format: request.format,
                    error_message: None,
                }
            }
            Err(e) => {
                error!("Screenshot capture failed: {:?}", e);
                ScreenshotResponse {
                    success: false,
                    data: Vec::new(),
                    width: 0,
                    height: 0,
                    format: request.format,
                    error_message: Some(format!("Screenshot failed: {:?}", e)),
                }
            }
        }
    }

    /// 发送响应给客户端
    fn send_response(pipe_handle: HANDLE, response: &ScreenshotResponse) -> Result<(), BypassError> {
        let response_data = serde_json::to_vec(response)
            .map_err(|e| BypassError::OperationFailed(format!("Failed to serialize response: {}", e)))?;

        let mut bytes_written: DWORD = 0;
        let success = unsafe {
            WriteFile(
                pipe_handle,
                response_data.as_ptr() as *const winapi::ctypes::c_void,
                response_data.len() as DWORD,
                &mut bytes_written,
                std::ptr::null_mut(),
            )
        };

        if success == FALSE {
            return Err(BypassError::OperationFailed(format!(
                "Failed to write to pipe: {}",
                unsafe { winapi::um::errhandlingapi::GetLastError() }
            )));
        }

        debug!("Sent response: {} bytes", bytes_written);
        Ok(())
    }

    /// 发送错误响应
    fn send_error_response(pipe_handle: HANDLE, error_message: &str) {
        let error_response = ScreenshotResponse {
            success: false,
            data: Vec::new(),
            width: 0,
            height: 0,
            format: ImageFormat::JPEG,
            error_message: Some(error_message.to_string()),
        };

        if let Err(e) = Self::send_response(pipe_handle, &error_response) {
            error!("Failed to send error response: {:?}", e);
        }
    }
}
