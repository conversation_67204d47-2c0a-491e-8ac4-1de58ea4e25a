# 🚀 CS安全截图插件 - 快速开始指南

## 📁 文件说明

- `screenshot_plugin.cna` - Cobalt Strike Aggressor脚本
- `screenshot_bof.o` - 编译好的BOF对象文件
- `README.md` - 详细使用指南

## ⚡ 快速部署

### 1. 启动Rust加载器
确保您的Rust加载器正在运行并启用了截图服务：
```bash
.\rust-bypassAV.exe
```

### 2. 加载CS插件
在Cobalt Strike客户端中：
1. 打开 `Script Manager`
2. 点击 `Load`
3. 选择 `screenshot_plugin.cna`
4. 确认加载成功

### 3. 使用安全截图
在任何Beacon中：
```bash
# 基础截图
beacon> screenshot

# 高质量JPEG
beacon> screenshot 0 95

# PNG格式
beacon> screenshot 1

# 限制尺寸
beacon> screenshot 0 85 0 1920 1080

# 测试连接
beacon> screenshot_test

# 查看配置
beacon> screenshot_config
```

## 🎯 参数说明

| 参数 | 说明 | 默认值 |
|------|------|--------|
| format | 0=JPEG, 1=PNG, 2=BMP | 0 |
| quality | JPEG质量 (1-100) | 85 |
| monitor | 显示器编号 (0=主显示器) | 0 |
| max_width | 最大宽度 (0=不限制) | 0 |
| max_height | 最大高度 (0=不限制) | 0 |

## 🔧 配置命令

```bash
# 查看当前设置
screenshot_config

# 设置PNG格式
screenshot_config format 1

# 设置JPEG质量
screenshot_config quality 95

# 设置尺寸限制
screenshot_config max_width 1920
screenshot_config max_height 1080
```

## 🛠️ 故障排除

### 连接失败
```bash
[!] Failed to connect to screenshot service
```
**解决方案**：
1. 确认Rust加载器正在运行
2. 检查截图服务是否启用
3. 以管理员权限运行

### 权限问题
```bash
[!] Failed to initialize screenshot manager
```
**解决方案**：
1. 以管理员权限运行Rust加载器
2. 确认在桌面会话中运行（非服务会话）

### 测试连接
```bash
beacon> screenshot_test
```
使用此命令测试与截图服务的连接。

## 🎉 优势

✅ **零跨进程注入** - 完全在Beacon进程内执行  
✅ **绕过EDR检测** - 无VirtualAllocEx/CreateRemoteThread调用  
✅ **保持CS兼容性** - 使用方式与原生screenshot完全相同  
✅ **高性能** - DXGI直接访问GPU缓冲区  
✅ **多格式支持** - JPEG/PNG/BMP格式可选  

## 📞 技术支持

如遇问题，请检查：
1. Rust加载器日志
2. CS客户端Script Console输出
3. 参考完整的README.md文档

---
**注意**：此插件需要配合专门的Rust加载器使用，确保加载器已正确配置截图服务。
