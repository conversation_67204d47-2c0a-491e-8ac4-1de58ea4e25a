@echo off
REM ============================================================================
REM CS安全截图插件部署脚本
REM 
REM 此脚本帮助您快速部署CS安全截图插件到Cobalt Strike环境
REM 
REM 作者: Rust Bypass Team
REM 版本: 1.0
REM ============================================================================

setlocal enabledelayedexpansion

echo.
echo ========================================
echo   CS安全截图插件部署脚本 v1.0
echo ========================================
echo.

REM 设置颜色输出
set "GREEN=[92m"
set "RED=[91m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "NC=[0m"

echo %BLUE%[1/4] 检查插件文件...%NC%

REM 检查必要文件
if not exist "cs_plugin_dist\screenshot_plugin.cna" (
    echo %RED%错误: 未找到screenshot_plugin.cna文件%NC%
    echo %RED%请先运行编译脚本生成插件文件%NC%
    pause
    exit /b 1
)

if not exist "cs_plugin_dist\screenshot_bof.o" (
    echo %RED%错误: 未找到screenshot_bof.o文件%NC%
    echo %RED%请先运行编译脚本生成BOF文件%NC%
    pause
    exit /b 1
)

echo %GREEN%✓ 插件文件检查完成%NC%
echo.

echo %BLUE%[2/4] 显示文件信息...%NC%
echo 插件文件列表:
dir cs_plugin_dist
echo.

echo %BLUE%[3/4] 部署选项...%NC%
echo.
echo 请选择部署方式:
echo 1. 手动部署 (推荐)
echo 2. 自动复制到CS目录 (需要指定CS路径)
echo 3. 仅显示使用说明
echo.

set /p "choice=请输入选择 (1-3): "

if "%choice%"=="1" goto manual_deploy
if "%choice%"=="2" goto auto_deploy
if "%choice%"=="3" goto show_usage
goto invalid_choice

:manual_deploy
echo.
echo %BLUE%手动部署步骤:%NC%
echo.
echo %YELLOW%1. 复制插件文件到CS脚本目录:%NC%
echo    - 将 cs_plugin_dist\ 目录下的所有文件
echo    - 复制到您的 Cobalt Strike scripts 目录
echo.
echo %YELLOW%2. 在CS客户端中加载插件:%NC%
echo    - 打开 Cobalt Strike 客户端
echo    - 点击 Script Manager
echo    - 点击 Load 按钮
echo    - 选择 screenshot_plugin.cna 文件
echo.
echo %YELLOW%3. 验证插件加载:%NC%
echo    - 在任意Beacon中输入: help screenshot
echo    - 应该看到新的screenshot命令帮助
echo.
goto end_deploy

:auto_deploy
echo.
set /p "cs_path=请输入CS安装目录路径 (例如: C:\CobaltStrike): "

if not exist "%cs_path%" (
    echo %RED%错误: 指定的CS目录不存在%NC%
    goto manual_deploy
)

REM 尝试找到scripts目录
set "scripts_dir=%cs_path%\scripts"
if not exist "%scripts_dir%" (
    mkdir "%scripts_dir%"
    echo %YELLOW%创建了scripts目录: %scripts_dir%%NC%
)

echo %BLUE%正在复制文件到: %scripts_dir%%NC%
copy cs_plugin_dist\*.* "%scripts_dir%\" >nul

if errorlevel 1 (
    echo %RED%复制失败，请手动复制文件%NC%
    goto manual_deploy
) else (
    echo %GREEN%✓ 文件复制成功%NC%
    echo.
    echo %YELLOW%下一步:%NC%
    echo 1. 启动Cobalt Strike客户端
    echo 2. Script Manager -> Load -> screenshot_plugin.cna
    echo.
)
goto end_deploy

:show_usage
echo.
echo %BLUE%使用说明:%NC%
echo.
echo %YELLOW%前置条件:%NC%
echo 1. 确保Rust加载器正在运行
echo 2. 确保截图服务已启用
echo.
echo %YELLOW%基础使用:%NC%
echo beacon^> screenshot                    # 默认截图
echo beacon^> screenshot 0 95               # 高质量JPEG
echo beacon^> screenshot 1                  # PNG格式
echo beacon^> screenshot 0 85 0 1920 1080   # 限制尺寸
echo.
echo %YELLOW%配置命令:%NC%
echo beacon^> screenshot_config             # 查看设置
echo beacon^> screenshot_config format 1   # 设置PNG格式
echo beacon^> screenshot_test              # 测试连接
echo.
goto end_deploy

:invalid_choice
echo %RED%无效选择，请重新运行脚本%NC%
pause
exit /b 1

:end_deploy
echo %BLUE%[4/4] 部署完成%NC%
echo.
echo %GREEN%========================================%NC%
echo %GREEN%部署完成！%NC%
echo %GREEN%========================================%NC%
echo.
echo %BLUE%重要提醒:%NC%
echo 1. %YELLOW%确保Rust加载器正在运行%NC%
echo 2. %YELLOW%在CS中加载screenshot_plugin.cna%NC%
echo 3. %YELLOW%使用screenshot命令进行安全截图%NC%
echo.
echo %BLUE%文档位置:%NC%
echo - 快速开始: cs_plugin_dist\QUICK_START.md
echo - 详细文档: cs_plugin_dist\README.md
echo.
echo %BLUE%测试命令:%NC%
echo beacon^> screenshot_test
echo.

REM 询问是否打开文档
set /p "open_doc=是否打开快速开始文档? (y/N): "
if /i "%open_doc%"=="y" (
    start notepad cs_plugin_dist\QUICK_START.md
)

echo.
echo %GREEN%感谢使用CS安全截图插件！%NC%
pause
