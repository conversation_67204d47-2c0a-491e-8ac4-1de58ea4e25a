// src/core/screenshot.rs
use std::sync::{Arc, Mutex};
use winapi::um::winuser::{GetSystemMetrics, GetDC, SM_CXSCREEN, SM_CYSCREEN};
use winapi::um::wingdi::{CreateCompatibleDC, CreateCompatibleBitmap, SelectObject, BitBlt, GetDIBits, SRCCOPY, BITMAPI<PERSON><PERSON>EADER, BITMAPINFO, DIB_RGB_COLORS, BI_RGB};
use winapi::shared::windef::{HDC, HBITMAP, HWND};
use winapi::shared::minwindef::{UINT, DWORD};
use winapi::shared::ntdef::NULL;
use log::{info, error, debug, warn};
use image::{ImageBuffer, RgbImage, DynamicImage, ImageFormat as ImgFormat};
use std::io::Cursor;

use crate::core::screenshot_service::{ScreenshotRequest, ImageFormat};
use crate::error::BypassError;

/// 截图管理器
pub struct ScreenshotManager {
    initialized: bool,
}

/// 截图错误类型
#[derive(Debug)]
pub enum ScreenshotError {
    InitializationFailed(String),
    CaptureError(String),
    CompressionError(String),
    UnsupportedFormat(String),
}

impl From<ScreenshotError> for BypassError {
    fn from(err: ScreenshotError) -> Self {
        BypassError::OperationFailed(format!("Screenshot error: {:?}", err))
    }
}

impl ScreenshotManager {
    /// 创建新的截图管理器
    pub fn new() -> Result<Self, ScreenshotError> {
        info!("Initializing screenshot manager...");
        
        // 检查是否有桌面访问权限
        let screen_width = unsafe { GetSystemMetrics(SM_CXSCREEN) };
        let screen_height = unsafe { GetSystemMetrics(SM_CYSCREEN) };
        
        if screen_width <= 0 || screen_height <= 0 {
            return Err(ScreenshotError::InitializationFailed(
                "Cannot access desktop or invalid screen dimensions".to_string()
            ));
        }
        
        info!("Screenshot manager initialized. Screen: {}x{}", screen_width, screen_height);
        Ok(Self {
            initialized: true,
        })
    }

    /// 基础截图功能
    pub fn capture_screen(&mut self) -> Result<Vec<u8>, ScreenshotError> {
        if !self.initialized {
            return Err(ScreenshotError::CaptureError("Manager not initialized".to_string()));
        }

        debug!("Starting screen capture...");
        
        // 获取屏幕尺寸
        let width = unsafe { GetSystemMetrics(SM_CXSCREEN) };
        let height = unsafe { GetSystemMetrics(SM_CYSCREEN) };
        
        if width <= 0 || height <= 0 {
            return Err(ScreenshotError::CaptureError("Invalid screen dimensions".to_string()));
        }

        // 使用GDI进行截图
        let screenshot_data = self.capture_with_gdi(width, height)?;
        
        // 转换为JPEG格式
        let jpeg_data = self.convert_to_jpeg(&screenshot_data, width as u32, height as u32, 85)?;
        
        info!("Screen capture completed: {}x{}, {} bytes", width, height, jpeg_data.len());
        Ok(jpeg_data)
    }

    /// 带选项的截图功能
    pub fn capture_screen_with_options(&mut self, request: &ScreenshotRequest) -> Result<(Vec<u8>, u32, u32), ScreenshotError> {
        if !self.initialized {
            return Err(ScreenshotError::CaptureError("Manager not initialized".to_string()));
        }

        debug!("Starting screen capture with options: {:?}", request);
        
        // 获取屏幕尺寸
        let screen_width = unsafe { GetSystemMetrics(SM_CXSCREEN) };
        let screen_height = unsafe { GetSystemMetrics(SM_CYSCREEN) };
        
        if screen_width <= 0 || screen_height <= 0 {
            return Err(ScreenshotError::CaptureError("Invalid screen dimensions".to_string()));
        }

        let mut width = screen_width as u32;
        let mut height = screen_height as u32;

        // 应用尺寸限制
        if request.max_width > 0 && width > request.max_width {
            height = (height * request.max_width) / width;
            width = request.max_width;
        }
        if request.max_height > 0 && height > request.max_height {
            width = (width * request.max_height) / height;
            height = request.max_height;
        }

        // 执行截图
        let screenshot_data = self.capture_with_gdi(screen_width, screen_height)?;
        
        // 如果需要缩放
        let final_data = if width != screen_width as u32 || height != screen_height as u32 {
            self.resize_image(&screenshot_data, screen_width as u32, screen_height as u32, width, height)?
        } else {
            screenshot_data
        };

        // 转换为指定格式
        let compressed_data = match request.format {
            ImageFormat::JPEG => self.convert_to_jpeg(&final_data, width, height, request.quality)?,
            ImageFormat::PNG => self.convert_to_png(&final_data, width, height)?,
            ImageFormat::BMP => self.convert_to_bmp(&final_data, width, height)?,
        };
        
        info!("Screen capture completed: {}x{}, {} bytes, format: {:?}", 
              width, height, compressed_data.len(), request.format);
        
        Ok((compressed_data, width, height))
    }

    /// 使用GDI进行截图
    fn capture_with_gdi(&self, width: i32, height: i32) -> Result<Vec<u8>, ScreenshotError> {
        unsafe {
            // 获取屏幕DC
            let hdc_screen = GetDC(NULL as HWND);
            if hdc_screen.is_null() {
                return Err(ScreenshotError::CaptureError("Failed to get screen DC".to_string()));
            }

            // 创建兼容DC
            let hdc_mem = CreateCompatibleDC(hdc_screen);
            if hdc_mem.is_null() {
                return Err(ScreenshotError::CaptureError("Failed to create compatible DC".to_string()));
            }

            // 创建兼容位图
            let hbitmap = CreateCompatibleBitmap(hdc_screen, width, height);
            if hbitmap.is_null() {
                return Err(ScreenshotError::CaptureError("Failed to create compatible bitmap".to_string()));
            }

            // 选择位图到内存DC
            let old_bitmap = SelectObject(hdc_mem, hbitmap as *mut winapi::ctypes::c_void);

            // 执行位图复制
            let result = BitBlt(hdc_mem, 0, 0, width, height, hdc_screen, 0, 0, SRCCOPY);
            if result == 0 {
                return Err(ScreenshotError::CaptureError("BitBlt failed".to_string()));
            }

            // 准备BITMAPINFO结构
            let mut bmi = BITMAPINFO {
                bmiHeader: BITMAPINFOHEADER {
                    biSize: std::mem::size_of::<BITMAPINFOHEADER>() as DWORD,
                    biWidth: width,
                    biHeight: -height, // 负值表示自上而下的位图
                    biPlanes: 1,
                    biBitCount: 24, // 24位RGB
                    biCompression: BI_RGB,
                    biSizeImage: 0,
                    biXPelsPerMeter: 0,
                    biYPelsPerMeter: 0,
                    biClrUsed: 0,
                    biClrImportant: 0,
                },
                bmiColors: [winapi::um::wingdi::RGBQUAD { rgbBlue: 0, rgbGreen: 0, rgbRed: 0, rgbReserved: 0 }; 1],
            };

            // 计算图像数据大小
            let bytes_per_pixel = 3; // 24位RGB
            let row_size = ((width * bytes_per_pixel + 3) / 4) * 4; // 4字节对齐
            let image_size = (row_size * height) as usize;
            let mut image_data = vec![0u8; image_size];

            // 获取位图数据
            let lines_copied = GetDIBits(
                hdc_screen,
                hbitmap,
                0,
                height as UINT,
                image_data.as_mut_ptr() as *mut winapi::ctypes::c_void,
                &mut bmi,
                DIB_RGB_COLORS,
            );

            // 清理资源
            SelectObject(hdc_mem, old_bitmap);
            winapi::um::wingdi::DeleteObject(hbitmap as *mut winapi::ctypes::c_void);
            winapi::um::wingdi::DeleteDC(hdc_mem);
            winapi::um::winuser::ReleaseDC(NULL as HWND, hdc_screen);

            if lines_copied == 0 {
                return Err(ScreenshotError::CaptureError("GetDIBits failed".to_string()));
            }

            // 转换BGR到RGB
            self.convert_bgr_to_rgb(&mut image_data, width as u32, height as u32);

            debug!("GDI capture successful: {} bytes", image_data.len());
            Ok(image_data)
        }
    }

    /// 转换BGR到RGB格式
    fn convert_bgr_to_rgb(&self, data: &mut [u8], width: u32, height: u32) {
        let bytes_per_pixel = 3;
        let row_size = ((width * bytes_per_pixel + 3) / 4) * 4; // 4字节对齐
        
        for y in 0..height {
            let row_start = (y * row_size) as usize;
            for x in 0..width {
                let pixel_start = row_start + (x * bytes_per_pixel) as usize;
                if pixel_start + 2 < data.len() {
                    // 交换B和R通道
                    data.swap(pixel_start, pixel_start + 2);
                }
            }
        }
    }

    /// 缩放图像
    fn resize_image(&self, data: &[u8], src_width: u32, src_height: u32, dst_width: u32, dst_height: u32) -> Result<Vec<u8>, ScreenshotError> {
        // 创建图像缓冲区
        let img_buffer = ImageBuffer::from_raw(src_width, src_height, data.to_vec())
            .ok_or_else(|| ScreenshotError::CompressionError("Failed to create image buffer".to_string()))?;
        
        let dynamic_img = DynamicImage::ImageRgb8(img_buffer);
        let resized = dynamic_img.resize(dst_width, dst_height, image::imageops::FilterType::Lanczos3);
        
        Ok(resized.to_rgb8().into_raw())
    }

    /// 转换为JPEG格式
    fn convert_to_jpeg(&self, data: &[u8], width: u32, height: u32, quality: u8) -> Result<Vec<u8>, ScreenshotError> {
        let img_buffer = ImageBuffer::from_raw(width, height, data.to_vec())
            .ok_or_else(|| ScreenshotError::CompressionError("Failed to create image buffer".to_string()))?;
        
        let dynamic_img = DynamicImage::ImageRgb8(img_buffer);
        let mut jpeg_data = Vec::new();
        let mut cursor = Cursor::new(&mut jpeg_data);
        
        dynamic_img.write_to(&mut cursor, ImgFormat::Jpeg)
            .map_err(|e| ScreenshotError::CompressionError(format!("JPEG compression failed: {}", e)))?;
        
        debug!("JPEG compression completed: {} -> {} bytes (quality: {})", 
               data.len(), jpeg_data.len(), quality);
        Ok(jpeg_data)
    }

    /// 转换为PNG格式
    fn convert_to_png(&self, data: &[u8], width: u32, height: u32) -> Result<Vec<u8>, ScreenshotError> {
        let img_buffer = ImageBuffer::from_raw(width, height, data.to_vec())
            .ok_or_else(|| ScreenshotError::CompressionError("Failed to create image buffer".to_string()))?;
        
        let dynamic_img = DynamicImage::ImageRgb8(img_buffer);
        let mut png_data = Vec::new();
        let mut cursor = Cursor::new(&mut png_data);
        
        dynamic_img.write_to(&mut cursor, ImgFormat::Png)
            .map_err(|e| ScreenshotError::CompressionError(format!("PNG compression failed: {}", e)))?;
        
        debug!("PNG compression completed: {} -> {} bytes", data.len(), png_data.len());
        Ok(png_data)
    }

    /// 转换为BMP格式
    fn convert_to_bmp(&self, data: &[u8], width: u32, height: u32) -> Result<Vec<u8>, ScreenshotError> {
        let img_buffer = ImageBuffer::from_raw(width, height, data.to_vec())
            .ok_or_else(|| ScreenshotError::CompressionError("Failed to create image buffer".to_string()))?;
        
        let dynamic_img = DynamicImage::ImageRgb8(img_buffer);
        let mut bmp_data = Vec::new();
        let mut cursor = Cursor::new(&mut bmp_data);
        
        dynamic_img.write_to(&mut cursor, ImgFormat::Bmp)
            .map_err(|e| ScreenshotError::CompressionError(format!("BMP compression failed: {}", e)))?;
        
        debug!("BMP compression completed: {} -> {} bytes", data.len(), bmp_data.len());
        Ok(bmp_data)
    }
}

impl Default for ScreenshotManager {
    fn default() -> Self {
        Self::new().unwrap_or_else(|_| Self { initialized: false })
    }
}
