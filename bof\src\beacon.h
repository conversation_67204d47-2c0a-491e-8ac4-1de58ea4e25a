/**
 * Beacon API Header for BOF Development
 * 
 * This header provides the necessary declarations for developing
 * Beacon Object Files (BOFs) for Cobalt Strike.
 */

#ifndef BEACON_H
#define BEACON_H

#include <windows.h>

// Beacon callback types
#define CALLBACK_OUTPUT      0x0
#define CALLBACK_KEYSTROKES  0x1
#define CALLBACK_FILE        0x2
#define CALLBACK_SCREENSHOT  0x3
#define CALLBACK_CLOSE       0x4
#define CALLBACK_READ        0x5
#define CALLBACK_CONNECT     0x6
#define CALLBACK_PING        0x7
#define CALLBACK_FILE_WRITE  0x8
#define CALLBACK_FILE_CLOSE  0x9
#define CALLBACK_PIPE_OPEN   0xa
#define CALLBACK_PIPE_CLOSE  0xb
#define CALLBACK_PIPE_READ   0xc
#define CALLBACK_POST        0xd
#define CALLBACK_ERROR       0xe
#define CALLBACK_IMPERSONATE 0xf
#define CALLBACK_DIE         0x10
#define CALLBACK_SSH_STATUS  0x11

// Data parser structure
typedef struct {
    char* original;
    char* buffer;
    int length;
    int size;
} datap;

// Beacon API function declarations
DECLSPEC_IMPORT void __cdecl BeaconDataParse(datap* parser, char* buffer, int size);
DECLSPEC_IMPORT int __cdecl BeaconDataInt(datap* parser);
DECLSPEC_IMPORT short __cdecl BeaconDataShort(datap* parser);
DECLSPEC_IMPORT int __cdecl BeaconDataLength(datap* parser);
DECLSPEC_IMPORT char* __cdecl BeaconDataExtract(datap* parser, int* size);

DECLSPEC_IMPORT void __cdecl BeaconPrintf(int type, char* fmt, ...);
DECLSPEC_IMPORT void __cdecl BeaconOutput(int type, char* data, int len);

DECLSPEC_IMPORT void __cdecl BeaconFormatAlloc(formatp* format, int maxsz);
DECLSPEC_IMPORT void __cdecl BeaconFormatReset(formatp* format);
DECLSPEC_IMPORT void __cdecl BeaconFormatFree(formatp* format);
DECLSPEC_IMPORT void __cdecl BeaconFormatAppend(formatp* format, char* text, int len);
DECLSPEC_IMPORT void __cdecl BeaconFormatPrintf(formatp* format, char* fmt, ...);
DECLSPEC_IMPORT char* __cdecl BeaconFormatToString(formatp* format, int* size);
DECLSPEC_IMPORT void __cdecl BeaconFormatInt(formatp* format, int value);

// Format structure for advanced output formatting
typedef struct {
    char* original;
    char* buffer;
    int length;
    int size;
} formatp;

// Memory allocation functions
DECLSPEC_IMPORT LPVOID __cdecl BeaconVirtualAlloc(LPVOID lpAddress, SIZE_T dwSize, DWORD flAllocationType, DWORD flProtect);
DECLSPEC_IMPORT BOOL __cdecl BeaconVirtualFree(LPVOID lpAddress, SIZE_T dwSize, DWORD dwFreeType);
DECLSPEC_IMPORT BOOL __cdecl BeaconVirtualProtect(LPVOID lpAddress, SIZE_T dwSize, DWORD flNewProtect, PDWORD lpflOldProtect);

// Process and thread functions
DECLSPEC_IMPORT HANDLE __cdecl BeaconGetCurrentProcess(void);
DECLSPEC_IMPORT DWORD __cdecl BeaconGetCurrentProcessId(void);
DECLSPEC_IMPORT HANDLE __cdecl BeaconGetCurrentThread(void);
DECLSPEC_IMPORT DWORD __cdecl BeaconGetCurrentThreadId(void);

// Utility macros
#define BOFSTART() \
    do { \
        BeaconPrintf(CALLBACK_OUTPUT, "[*] BOF execution started"); \
    } while(0)

#define BOFEND() \
    do { \
        BeaconPrintf(CALLBACK_OUTPUT, "[*] BOF execution completed"); \
    } while(0)

#define BOFERROR(msg) \
    do { \
        BeaconPrintf(CALLBACK_ERROR, "[!] Error: %s", msg); \
    } while(0)

#define BOFINFO(msg) \
    do { \
        BeaconPrintf(CALLBACK_OUTPUT, "[*] %s", msg); \
    } while(0)

#define BOFSUCCESS(msg) \
    do { \
        BeaconPrintf(CALLBACK_OUTPUT, "[+] %s", msg); \
    } while(0)

// Standard library function replacements for BOF
// These should be imported from appropriate DLLs
DECLSPEC_IMPORT int __cdecl MSVCRT$sprintf(char* buffer, const char* format, ...);
DECLSPEC_IMPORT int __cdecl MSVCRT$strlen(const char* str);
DECLSPEC_IMPORT char* __cdecl MSVCRT$strcpy(char* dest, const char* src);
DECLSPEC_IMPORT char* __cdecl MSVCRT$strcat(char* dest, const char* src);
DECLSPEC_IMPORT char* __cdecl MSVCRT$strstr(const char* haystack, const char* needle);
DECLSPEC_IMPORT int __cdecl MSVCRT$atoi(const char* str);
DECLSPEC_IMPORT void* __cdecl MSVCRT$malloc(size_t size);
DECLSPEC_IMPORT void __cdecl MSVCRT$free(void* ptr);
DECLSPEC_IMPORT void* __cdecl MSVCRT$memcpy(void* dest, const void* src, size_t n);
DECLSPEC_IMPORT void* __cdecl MSVCRT$memset(void* s, int c, size_t n);

// Convenience macros for standard functions
#define sprintf MSVCRT$sprintf
#define strlen MSVCRT$strlen
#define strcpy MSVCRT$strcpy
#define strcat MSVCRT$strcat
#define strstr MSVCRT$strstr
#define atoi MSVCRT$atoi
#define malloc MSVCRT$malloc
#define free MSVCRT$free
#define memcpy MSVCRT$memcpy
#define memset MSVCRT$memset

#endif // BEACON_H
