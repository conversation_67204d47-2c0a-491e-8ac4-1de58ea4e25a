C:\Users\<USER>\Desktop\rust_bypass1-5.29\target\debug\deps\rust_bypassAV-1ae87a5df2e0b2e4.d: src\lib.rs src\config\mod.rs src\config\defense_config.rs src\config\execution_config.rs src\config\memory_config.rs src\config\sandbox_config.rs src\config\loader.rs src\core\mod.rs src\core\memory.rs src\core\syscalls.rs src\core\execution.rs src\core\crypto.rs src\core\encrypted_memory.rs src\core\shellcode_loader.rs src\core\embedded.rs src\core\screenshot.rs src\core\screenshot_service.rs src\error\mod.rs src\utils\mod.rs src\utils\code_cave_scanner.rs src\utils\logger.rs src\utils\pe_parser.rs src\utils\random.rs src\utils\winapi.rs src\utils\winapi_utils.rs src\defense\mod.rs src\defense\amsi.rs src\defense\cmdline.rs src\defense\etw.rs src\defense\identity.rs src\defense\ppid.rs src\defense\sandbox.rs src\defense\evasion_manager.rs src\orchestrator.rs src\constants.rs

C:\Users\<USER>\Desktop\rust_bypass1-5.29\target\debug\deps\librust_bypassAV-1ae87a5df2e0b2e4.rmeta: src\lib.rs src\config\mod.rs src\config\defense_config.rs src\config\execution_config.rs src\config\memory_config.rs src\config\sandbox_config.rs src\config\loader.rs src\core\mod.rs src\core\memory.rs src\core\syscalls.rs src\core\execution.rs src\core\crypto.rs src\core\encrypted_memory.rs src\core\shellcode_loader.rs src\core\embedded.rs src\core\screenshot.rs src\core\screenshot_service.rs src\error\mod.rs src\utils\mod.rs src\utils\code_cave_scanner.rs src\utils\logger.rs src\utils\pe_parser.rs src\utils\random.rs src\utils\winapi.rs src\utils\winapi_utils.rs src\defense\mod.rs src\defense\amsi.rs src\defense\cmdline.rs src\defense\etw.rs src\defense\identity.rs src\defense\ppid.rs src\defense\sandbox.rs src\defense\evasion_manager.rs src\orchestrator.rs src\constants.rs

src\lib.rs:
src\config\mod.rs:
src\config\defense_config.rs:
src\config\execution_config.rs:
src\config\memory_config.rs:
src\config\sandbox_config.rs:
src\config\loader.rs:
src\core\mod.rs:
src\core\memory.rs:
src\core\syscalls.rs:
src\core\execution.rs:
src\core\crypto.rs:
src\core\encrypted_memory.rs:
src\core\shellcode_loader.rs:
src\core\embedded.rs:
src\core\screenshot.rs:
src\core\screenshot_service.rs:
src\error\mod.rs:
src\utils\mod.rs:
src\utils\code_cave_scanner.rs:
src\utils\logger.rs:
src\utils\pe_parser.rs:
src\utils\random.rs:
src\utils\winapi.rs:
src\utils\winapi_utils.rs:
src\defense\mod.rs:
src\defense\amsi.rs:
src\defense\cmdline.rs:
src\defense\etw.rs:
src\defense\identity.rs:
src\defense\ppid.rs:
src\defense\sandbox.rs:
src\defense\evasion_manager.rs:
src\orchestrator.rs:
src\constants.rs:
