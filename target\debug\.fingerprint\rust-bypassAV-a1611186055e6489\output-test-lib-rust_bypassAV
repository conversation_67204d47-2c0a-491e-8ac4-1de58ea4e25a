{"$message_type":"diagnostic","message":"unresolved import `crate::core::screenshot`","code":{"code":"E0432","explanation":"An import was unresolved.\n\nErroneous code example:\n\n```compile_fail,E0432\nuse something::Foo; // error: unresolved import `something::Foo`.\n```\n\nIn Rust 2015, paths in `use` statements are relative to the crate root. To\nimport items relative to the current and parent modules, use the `self::` and\n`super::` prefixes, respectively.\n\nIn Rust 2018 or later, paths in `use` statements are relative to the current\nmodule unless they begin with the name of a crate or a literal `crate::`, in\nwhich case they start from the crate root. As in Rust 2015 code, the `self::`\nand `super::` prefixes refer to the current and parent modules respectively.\n\nAlso verify that you didn't misspell the import name and that the import exists\nin the module from where you tried to import it. Example:\n\n```\nuse self::something::Foo; // Ok.\n\nmod something {\n    pub struct Foo;\n}\n# fn main() {}\n```\n\nIf you tried to use a module from an external crate and are using Rust 2015,\nyou may have missed the `extern crate` declaration (which is usually placed in\nthe crate root):\n\n```edition2015\nextern crate core; // Required to use the `core` crate in Rust 2015.\n\nuse core::any;\n# fn main() {}\n```\n\nSince Rust 2018 the `extern crate` declaration is not required and\nyou can instead just `use` it:\n\n```edition2018\nuse core::any; // No extern crate required in Rust 2018.\n# fn main() {}\n```\n"},"level":"error","spans":[{"file_name":"src\\orchestrator.rs","byte_start":451,"byte_end":461,"line_start":11,"line_end":11,"column_start":18,"column_end":28,"is_primary":true,"text":[{"text":"use crate::core::screenshot::{ScreenshotManager, ScreenshotError};","highlight_start":18,"highlight_end":28}],"label":"could not find `screenshot` in `core`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0432]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unresolved import `crate::core::screenshot`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\orchestrator.rs:11:18\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m11\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::core::screenshot::{ScreenshotManager, ScreenshotError};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mcould not find `screenshot` in `core`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unresolved import `crate::core::screenshot_service`","code":{"code":"E0432","explanation":"An import was unresolved.\n\nErroneous code example:\n\n```compile_fail,E0432\nuse something::Foo; // error: unresolved import `something::Foo`.\n```\n\nIn Rust 2015, paths in `use` statements are relative to the crate root. To\nimport items relative to the current and parent modules, use the `self::` and\n`super::` prefixes, respectively.\n\nIn Rust 2018 or later, paths in `use` statements are relative to the current\nmodule unless they begin with the name of a crate or a literal `crate::`, in\nwhich case they start from the crate root. As in Rust 2015 code, the `self::`\nand `super::` prefixes refer to the current and parent modules respectively.\n\nAlso verify that you didn't misspell the import name and that the import exists\nin the module from where you tried to import it. Example:\n\n```\nuse self::something::Foo; // Ok.\n\nmod something {\n    pub struct Foo;\n}\n# fn main() {}\n```\n\nIf you tried to use a module from an external crate and are using Rust 2015,\nyou may have missed the `extern crate` declaration (which is usually placed in\nthe crate root):\n\n```edition2015\nextern crate core; // Required to use the `core` crate in Rust 2015.\n\nuse core::any;\n# fn main() {}\n```\n\nSince Rust 2018 the `extern crate` declaration is not required and\nyou can instead just `use` it:\n\n```edition2018\nuse core::any; // No extern crate required in Rust 2018.\n# fn main() {}\n```\n"},"level":"error","spans":[{"file_name":"src\\orchestrator.rs","byte_start":519,"byte_end":537,"line_start":12,"line_end":12,"column_start":18,"column_end":36,"is_primary":true,"text":[{"text":"use crate::core::screenshot_service::{ScreenshotService, ScreenshotServiceConfig};","highlight_start":18,"highlight_end":36}],"label":"could not find `screenshot_service` in `core`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0432]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unresolved import `crate::core::screenshot_service`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\orchestrator.rs:12:18\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m12\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::core::screenshot_service::{ScreenshotService, ScreenshotServiceConfig};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mcould not find `screenshot_service` in `core`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `crate::core::syscalls::SyscallManager`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\core\\execution.rs","byte_start":243,"byte_end":280,"line_start":9,"line_end":9,"column_start":5,"column_end":42,"is_primary":true,"text":[{"text":"use crate::core::syscalls::SyscallManager;","highlight_start":5,"highlight_end":42}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\core\\execution.rs","byte_start":239,"byte_end":283,"line_start":9,"line_end":10,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use crate::core::syscalls::SyscallManager;","highlight_start":1,"highlight_end":43},{"text":"use crate::config::execution_config::{","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `crate::core::syscalls::SyscallManager`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\core\\execution.rs:9:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m9\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::core::syscalls::SyscallManager;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `BypassResult`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\core\\execution.rs","byte_start":570,"byte_end":582,"line_start":15,"line_end":15,"column_start":33,"column_end":45,"is_primary":true,"text":[{"text":"use crate::error::{BypassError, BypassResult};","highlight_start":33,"highlight_end":45}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\core\\execution.rs","byte_start":568,"byte_end":582,"line_start":15,"line_end":15,"column_start":31,"column_end":45,"is_primary":true,"text":[{"text":"use crate::error::{BypassError, BypassResult};","highlight_start":31,"highlight_end":45}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\core\\execution.rs","byte_start":556,"byte_end":557,"line_start":15,"line_end":15,"column_start":19,"column_end":20,"is_primary":true,"text":[{"text":"use crate::error::{BypassError, BypassResult};","highlight_start":19,"highlight_end":20}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\core\\execution.rs","byte_start":582,"byte_end":583,"line_start":15,"line_end":15,"column_start":45,"column_end":46,"is_primary":true,"text":[{"text":"use crate::error::{BypassError, BypassResult};","highlight_start":45,"highlight_end":46}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `BypassResult`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\core\\execution.rs:15:33\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m15\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::error::{BypassError, BypassResult};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `crate::utils::random::static_random::get_random_u32`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\core\\execution.rs","byte_start":705,"byte_end":756,"line_start":19,"line_end":19,"column_start":5,"column_end":56,"is_primary":true,"text":[{"text":"use crate::utils::random::static_random::get_random_u32;","highlight_start":5,"highlight_end":56}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\core\\execution.rs","byte_start":701,"byte_end":759,"line_start":19,"line_end":20,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use crate::utils::random::static_random::get_random_u32;","highlight_start":1,"highlight_end":57},{"text":"use windows_sys::Win32::System::Threading::{","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `crate::utils::random::static_random::get_random_u32`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\core\\execution.rs:19:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m19\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::utils::random::static_random::get_random_u32;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `std::fs`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\core\\shellcode_loader.rs","byte_start":4,"byte_end":11,"line_start":1,"line_end":1,"column_start":5,"column_end":12,"is_primary":true,"text":[{"text":"use std::fs; // 虽然未使用，但暂时保留以避免引入更多更改","highlight_start":5,"highlight_end":12}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\core\\shellcode_loader.rs","byte_start":0,"byte_end":12,"line_start":1,"line_end":1,"column_start":1,"column_end":13,"is_primary":true,"text":[{"text":"use std::fs; // 虽然未使用，但暂时保留以避免引入更多更改","highlight_start":1,"highlight_end":13}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `std::fs`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\core\\shellcode_loader.rs:1:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m1\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::fs; // 虽然未使用，但暂时保留以避免引入更多更改\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `engine::general_purpose`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\core\\shellcode_loader.rs","byte_start":158,"byte_end":181,"line_start":5,"line_end":5,"column_start":27,"column_end":50,"is_primary":true,"text":[{"text":"use base64::{Engine as _, engine::general_purpose};","highlight_start":27,"highlight_end":50}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\core\\shellcode_loader.rs","byte_start":156,"byte_end":181,"line_start":5,"line_end":5,"column_start":25,"column_end":50,"is_primary":true,"text":[{"text":"use base64::{Engine as _, engine::general_purpose};","highlight_start":25,"highlight_end":50}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\core\\shellcode_loader.rs","byte_start":144,"byte_end":145,"line_start":5,"line_end":5,"column_start":13,"column_end":14,"is_primary":true,"text":[{"text":"use base64::{Engine as _, engine::general_purpose};","highlight_start":13,"highlight_end":14}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\core\\shellcode_loader.rs","byte_start":181,"byte_end":182,"line_start":5,"line_end":5,"column_start":50,"column_end":51,"is_primary":true,"text":[{"text":"use base64::{Engine as _, engine::general_purpose};","highlight_start":50,"highlight_end":51}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `engine::general_purpose`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\core\\shellcode_loader.rs:5:27\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m5\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse base64::{Engine as _, engine::general_purpose};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"type `LPFIBER_START_ROUTINE` should have an upper camel case name","code":{"code":"non_camel_case_types","explanation":null},"level":"warning","spans":[{"file_name":"src\\utils\\winapi.rs","byte_start":9325,"byte_end":9346,"line_start":177,"line_end":177,"column_start":10,"column_end":31,"is_primary":true,"text":[{"text":"pub type LPFIBER_START_ROUTINE = Option<unsafe extern \"system\" fn(LPVOID) -> ()>;","highlight_start":10,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(non_camel_case_types)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"convert the identifier to upper camel case","code":null,"level":"help","spans":[{"file_name":"src\\utils\\winapi.rs","byte_start":9325,"byte_end":9346,"line_start":177,"line_end":177,"column_start":10,"column_end":31,"is_primary":true,"text":[{"text":"pub type LPFIBER_START_ROUTINE = Option<unsafe extern \"system\" fn(LPVOID) -> ()>;","highlight_start":10,"highlight_end":31}],"label":null,"suggested_replacement":"LpfiberStartRoutine","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: type `LPFIBER_START_ROUTINE` should have an upper camel case name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\utils\\winapi.rs:177:10\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m177\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub type LPFIBER_START_ROUTINE = Option<unsafe extern \"system\" fn(LPVOID) -> ()>;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to upper camel case: `LpfiberStartRoutine`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(non_camel_case_types)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `enums::*`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\defense\\sandbox.rs","byte_start":49459,"byte_end":49467,"line_start":1212,"line_end":1212,"column_start":18,"column_end":26,"is_primary":true,"text":[{"text":"    use winreg::{enums::*, RegKey}; // Import necessary enums","highlight_start":18,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\defense\\sandbox.rs","byte_start":49459,"byte_end":49469,"line_start":1212,"line_end":1212,"column_start":18,"column_end":28,"is_primary":true,"text":[{"text":"    use winreg::{enums::*, RegKey}; // Import necessary enums","highlight_start":18,"highlight_end":28}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\defense\\sandbox.rs","byte_start":49458,"byte_end":49459,"line_start":1212,"line_end":1212,"column_start":17,"column_end":18,"is_primary":true,"text":[{"text":"    use winreg::{enums::*, RegKey}; // Import necessary enums","highlight_start":17,"highlight_end":18}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\defense\\sandbox.rs","byte_start":49475,"byte_end":49476,"line_start":1212,"line_end":1212,"column_start":34,"column_end":35,"is_primary":true,"text":[{"text":"    use winreg::{enums::*, RegKey}; // Import necessary enums","highlight_start":34,"highlight_end":35}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `enums::*`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\defense\\sandbox.rs:1212:18\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m1212\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    use winreg::{enums::*, RegKey}; // Import necessary enums\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"use of deprecated function `base64::encode`: Use Engine::encode","code":{"code":"deprecated","explanation":null},"level":"warning","spans":[{"file_name":"src\\core\\crypto.rs","byte_start":13241,"byte_end":13247,"line_start":304,"line_end":304,"column_start":17,"column_end":23,"is_primary":true,"text":[{"text":"    use base64::encode as base64_encode; // Use `decode` from super","highlight_start":17,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(deprecated)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: use of deprecated function `base64::encode`: Use Engine::encode\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\core\\crypto.rs:304:17\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m304\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    use base64::encode as base64_encode; // Use `decode` from super\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(deprecated)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"use of deprecated function `base64::encode`: Use Engine::encode","code":{"code":"deprecated","explanation":null},"level":"warning","spans":[{"file_name":"src\\core\\crypto.rs","byte_start":16677,"byte_end":16690,"line_start":384,"line_end":384,"column_start":30,"column_end":43,"is_primary":true,"text":[{"text":"        let ciphertext_b64 = base64_encode(ciphertext_bytes);","highlight_start":30,"highlight_end":43}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: use of deprecated function `base64::encode`: Use Engine::encode\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\core\\crypto.rs:384:30\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m384\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let ciphertext_b64 = base64_encode(ciphertext_bytes);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"use of deprecated function `base64::encode`: Use Engine::encode","code":{"code":"deprecated","explanation":null},"level":"warning","spans":[{"file_name":"src\\core\\crypto.rs","byte_start":16733,"byte_end":16746,"line_start":385,"line_end":385,"column_start":23,"column_end":36,"is_primary":true,"text":[{"text":"        let key_b64 = base64_encode(&key);","highlight_start":23,"highlight_end":36}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: use of deprecated function `base64::encode`: Use Engine::encode\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\core\\crypto.rs:385:23\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m385\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let key_b64 = base64_encode(&key);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                       \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"use of deprecated function `base64::encode`: Use Engine::encode","code":{"code":"deprecated","explanation":null},"level":"warning","spans":[{"file_name":"src\\core\\crypto.rs","byte_start":16776,"byte_end":16789,"line_start":386,"line_end":386,"column_start":22,"column_end":35,"is_primary":true,"text":[{"text":"        let iv_b64 = base64_encode(&iv);","highlight_start":22,"highlight_end":35}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: use of deprecated function `base64::encode`: Use Engine::encode\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\core\\crypto.rs:386:22\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m386\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let iv_b64 = base64_encode(&iv);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"use of deprecated function `base64::encode`: Use Engine::encode","code":{"code":"deprecated","explanation":null},"level":"warning","spans":[{"file_name":"src\\core\\crypto.rs","byte_start":22676,"byte_end":22689,"line_start":520,"line_end":520,"column_start":30,"column_end":43,"is_primary":true,"text":[{"text":"        let ciphertext_b64 = base64_encode(ciphertext_bytes);","highlight_start":30,"highlight_end":43}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: use of deprecated function `base64::encode`: Use Engine::encode\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\core\\crypto.rs:520:30\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m520\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let ciphertext_b64 = base64_encode(ciphertext_bytes);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"use of deprecated function `base64::encode`: Use Engine::encode","code":{"code":"deprecated","explanation":null},"level":"warning","spans":[{"file_name":"src\\core\\crypto.rs","byte_start":22738,"byte_end":22751,"line_start":521,"line_end":521,"column_start":29,"column_end":42,"is_primary":true,"text":[{"text":"        let wrong_key_b64 = base64_encode(&wrong_key_bytes);","highlight_start":29,"highlight_end":42}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: use of deprecated function `base64::encode`: Use Engine::encode\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\core\\crypto.rs:521:29\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m521\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let wrong_key_b64 = base64_encode(&wrong_key_bytes);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"use of deprecated function `base64::encode`: Use Engine::encode","code":{"code":"deprecated","explanation":null},"level":"warning","spans":[{"file_name":"src\\core\\crypto.rs","byte_start":22793,"byte_end":22806,"line_start":522,"line_end":522,"column_start":22,"column_end":35,"is_primary":true,"text":[{"text":"        let iv_b64 = base64_encode(&iv_bytes);","highlight_start":22,"highlight_end":35}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: use of deprecated function `base64::encode`: Use Engine::encode\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\core\\crypto.rs:522:22\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m522\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let iv_b64 = base64_encode(&iv_bytes);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"value assigned to `thread_handle` is never read","code":{"code":"unused_assignments","explanation":null},"level":"warning","spans":[{"file_name":"src\\orchestrator.rs","byte_start":11168,"byte_end":11181,"line_start":258,"line_end":258,"column_start":17,"column_end":30,"is_primary":true,"text":[{"text":"        let mut thread_handle: winapi::shared::ntdef::HANDLE = std::ptr::null_mut();","highlight_start":17,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"maybe it is overwritten before being read?","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"`#[warn(unused_assignments)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: value assigned to `thread_handle` is never read\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\orchestrator.rs:258:17\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m258\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let mut thread_handle: winapi::shared::ntdef::HANDLE = std::ptr::null_mut();\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: maybe it is overwritten before being read?\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_assignments)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unnecessary `unsafe` block","code":{"code":"unused_unsafe","explanation":null},"level":"warning","spans":[{"file_name":"src\\core\\memory.rs","byte_start":45055,"byte_end":45061,"line_start":994,"line_end":994,"column_start":173,"column_end":179,"is_primary":true,"text":[{"text":"                                println!(\"[DEBUG_RESTORE] ModuleStompingAllocator (FunctionHook-DirectStomp): Bytes at target {:p} BEFORE restore: {:?}\", target_func_addr, unsafe { std::slice::from_raw_parts(target_func_addr as *const u8, original_func_bytes.len()) });","highlight_start":173,"highlight_end":179}],"label":"unnecessary `unsafe` block","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\core\\memory.rs","byte_start":44450,"byte_end":44456,"line_start":985,"line_end":985,"column_start":21,"column_end":27,"is_primary":false,"text":[{"text":"                    unsafe {","highlight_start":21,"highlight_end":27}],"label":"because it's nested under this `unsafe` block","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_unsafe)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unnecessary `unsafe` block\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\core\\memory.rs:994:173\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m985\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m   unsafe {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m       \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mbecause it's nested under this `unsafe` block\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m994\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m               println!(\"[DEBUG_RESTORE] ModuleStompingAllocator (FunctionHook-DirectStomp): Bytes at target {:p} BEFORE restore: {:?}\", target_func_addr, unsafe { \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                                                                                                                               \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11munnecessary `unsafe` block\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_unsafe)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unnecessary `unsafe` block","code":{"code":"unused_unsafe","explanation":null},"level":"warning","spans":[{"file_name":"src\\core\\memory.rs","byte_start":45739,"byte_end":45745,"line_start":1000,"line_end":1000,"column_start":67,"column_end":73,"is_primary":true,"text":[{"text":"                                let bytes_after_restore_attempt = unsafe { std::slice::from_raw_parts(target_func_addr as *const u8, original_func_bytes.len()) };","highlight_start":67,"highlight_end":73}],"label":"unnecessary `unsafe` block","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\core\\memory.rs","byte_start":44450,"byte_end":44456,"line_start":985,"line_end":985,"column_start":21,"column_end":27,"is_primary":false,"text":[{"text":"                    unsafe {","highlight_start":21,"highlight_end":27}],"label":"because it's nested under this `unsafe` block","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unnecessary `unsafe` block\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\core\\memory.rs:1000:67\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m985\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m   unsafe {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m       \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mbecause it's nested under this `unsafe` block\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m1000\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m               let bytes_after_restore_attempt = unsafe { std::slice::from_raw_parts(target_func_addr as *const u8, original_func_byte\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11munnecessary `unsafe` block\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variable does not need to be mutable","code":{"code":"unused_mut","explanation":null},"level":"warning","spans":[{"file_name":"src\\core\\memory.rs","byte_start":96906,"byte_end":96924,"line_start":2134,"line_end":2134,"column_start":13,"column_end":31,"is_primary":true,"text":[{"text":"        let mut mock_protector = MockProtector::new();","highlight_start":13,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_mut)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove this `mut`","code":null,"level":"help","spans":[{"file_name":"src\\core\\memory.rs","byte_start":96906,"byte_end":96910,"line_start":2134,"line_end":2134,"column_start":13,"column_end":17,"is_primary":true,"text":[{"text":"        let mut mock_protector = MockProtector::new();","highlight_start":13,"highlight_end":17}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: variable does not need to be mutable\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\core\\memory.rs:2134:13\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m2134\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let mut mock_protector = MockProtector::new();\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m----\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mhelp: remove this `mut`\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_mut)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unnecessary `unsafe` block","code":{"code":"unused_unsafe","explanation":null},"level":"warning","spans":[{"file_name":"src\\core\\syscalls.rs","byte_start":1895,"byte_end":1901,"line_start":42,"line_end":42,"column_start":33,"column_end":39,"is_primary":true,"text":[{"text":"                let field_ptr = unsafe { core::ptr::addr_of!((*base_ptr).$field) };","highlight_start":33,"highlight_end":39}],"label":"unnecessary `unsafe` block","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\core\\syscalls.rs","byte_start":14712,"byte_end":14783,"line_start":384,"line_end":384,"column_start":21,"column_end":92,"is_primary":false,"text":[{"text":"                    container_of!(current_entry, WS_LDR_DATA_TABLE_ENTRY, InLoadOrderLinks);","highlight_start":21,"highlight_end":92}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"container_of!","def_site_span":{"file_name":"src\\core\\syscalls.rs","byte_start":1420,"byte_end":1445,"line_start":33,"line_end":33,"column_start":1,"column_end":26,"is_primary":false,"text":[{"text":"macro_rules! container_of {","highlight_start":1,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},{"file_name":"src\\core\\syscalls.rs","byte_start":10317,"byte_end":10323,"line_start":287,"line_end":287,"column_start":27,"column_end":33,"is_primary":false,"text":[{"text":"        let find_result = unsafe {","highlight_start":27,"highlight_end":33}],"label":"because it's nested under this `unsafe` block","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unnecessary `unsafe` block\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\core\\syscalls.rs:42:33\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m42\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                let field_ptr = unsafe { core::ptr::addr_of!((*base_ptr).$field) };\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11munnecessary `unsafe` block\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m287\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let find_result = unsafe {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mbecause it's nested under this `unsafe` block\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m384\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                    container_of!(current_entry, WS_LDR_DATA_TABLE_ENTRY, InLoadOrderLinks);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-----------------------------------------------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14min this macro invocation\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: this warning originates in the macro `container_of` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"value assigned to `nt_status` is never read","code":{"code":"unused_assignments","explanation":null},"level":"warning","spans":[{"file_name":"src\\core\\syscalls.rs","byte_start":44516,"byte_end":44525,"line_start":954,"line_end":954,"column_start":17,"column_end":26,"is_primary":true,"text":[{"text":"        let mut nt_status: NTSTATUS = 0;","highlight_start":17,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"maybe it is overwritten before being read?","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: value assigned to `nt_status` is never read\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\core\\syscalls.rs:954:17\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m954\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let mut nt_status: NTSTATUS = 0;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: maybe it is overwritten before being read?\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"value assigned to `nt_status` is never read","code":{"code":"unused_assignments","explanation":null},"level":"warning","spans":[{"file_name":"src\\core\\syscalls.rs","byte_start":52025,"byte_end":52034,"line_start":1128,"line_end":1128,"column_start":17,"column_end":26,"is_primary":true,"text":[{"text":"        let mut nt_status: NTSTATUS = 0;","highlight_start":17,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"maybe it is overwritten before being read?","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: value assigned to `nt_status` is never read\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\core\\syscalls.rs:1128:17\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m1128\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let mut nt_status: NTSTATUS = 0;\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: maybe it is overwritten before being read?\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"value assigned to `nt_status` is never read","code":{"code":"unused_assignments","explanation":null},"level":"warning","spans":[{"file_name":"src\\core\\syscalls.rs","byte_start":54293,"byte_end":54302,"line_start":1185,"line_end":1185,"column_start":17,"column_end":26,"is_primary":true,"text":[{"text":"        let mut nt_status: NTSTATUS = 0;","highlight_start":17,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"maybe it is overwritten before being read?","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: value assigned to `nt_status` is never read\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\core\\syscalls.rs:1185:17\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m1185\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let mut nt_status: NTSTATUS = 0;\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: maybe it is overwritten before being read?\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"value assigned to `nt_status` is never read","code":{"code":"unused_assignments","explanation":null},"level":"warning","spans":[{"file_name":"src\\core\\syscalls.rs","byte_start":56195,"byte_end":56204,"line_start":1241,"line_end":1241,"column_start":17,"column_end":26,"is_primary":true,"text":[{"text":"        let mut nt_status: NTSTATUS = 0;","highlight_start":17,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"maybe it is overwritten before being read?","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: value assigned to `nt_status` is never read\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\core\\syscalls.rs:1241:17\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m1241\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let mut nt_status: NTSTATUS = 0;\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: maybe it is overwritten before being read?\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"value assigned to `nt_status` is never read","code":{"code":"unused_assignments","explanation":null},"level":"warning","spans":[{"file_name":"src\\core\\syscalls.rs","byte_start":58513,"byte_end":58522,"line_start":1305,"line_end":1305,"column_start":17,"column_end":26,"is_primary":true,"text":[{"text":"        let mut nt_status: NTSTATUS = 0;","highlight_start":17,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"maybe it is overwritten before being read?","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: value assigned to `nt_status` is never read\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\core\\syscalls.rs:1305:17\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m1305\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let mut nt_status: NTSTATUS = 0;\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: maybe it is overwritten before being read?\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"value assigned to `nt_status` is never read","code":{"code":"unused_assignments","explanation":null},"level":"warning","spans":[{"file_name":"src\\core\\syscalls.rs","byte_start":61810,"byte_end":61819,"line_start":1383,"line_end":1383,"column_start":17,"column_end":26,"is_primary":true,"text":[{"text":"        let mut nt_status: NTSTATUS = 0;","highlight_start":17,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"maybe it is overwritten before being read?","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: value assigned to `nt_status` is never read\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\core\\syscalls.rs:1383:17\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m1383\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let mut nt_status: NTSTATUS = 0;\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: maybe it is overwritten before being read?\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"value assigned to `nt_status` is never read","code":{"code":"unused_assignments","explanation":null},"level":"warning","spans":[{"file_name":"src\\core\\syscalls.rs","byte_start":65850,"byte_end":65859,"line_start":1479,"line_end":1479,"column_start":17,"column_end":26,"is_primary":true,"text":[{"text":"        let mut nt_status: NTSTATUS = 0;","highlight_start":17,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"maybe it is overwritten before being read?","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: value assigned to `nt_status` is never read\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\core\\syscalls.rs:1479:17\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m1479\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let mut nt_status: NTSTATUS = 0;\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: maybe it is overwritten before being read?\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"value assigned to `nt_status` is never read","code":{"code":"unused_assignments","explanation":null},"level":"warning","spans":[{"file_name":"src\\core\\syscalls.rs","byte_start":69672,"byte_end":69681,"line_start":1571,"line_end":1571,"column_start":17,"column_end":26,"is_primary":true,"text":[{"text":"        let mut nt_status: NTSTATUS = 0;","highlight_start":17,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"maybe it is overwritten before being read?","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: value assigned to `nt_status` is never read\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\core\\syscalls.rs:1571:17\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m1571\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let mut nt_status: NTSTATUS = 0;\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: maybe it is overwritten before being read?\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"value assigned to `nt_status` is never read","code":{"code":"unused_assignments","explanation":null},"level":"warning","spans":[{"file_name":"src\\core\\syscalls.rs","byte_start":71790,"byte_end":71799,"line_start":1632,"line_end":1632,"column_start":17,"column_end":26,"is_primary":true,"text":[{"text":"        let mut nt_status: NTSTATUS = 0;","highlight_start":17,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"maybe it is overwritten before being read?","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: value assigned to `nt_status` is never read\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\core\\syscalls.rs:1632:17\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m1632\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let mut nt_status: NTSTATUS = 0;\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: maybe it is overwritten before being read?\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"value assigned to `nt_status` is never read","code":{"code":"unused_assignments","explanation":null},"level":"warning","spans":[{"file_name":"src\\core\\syscalls.rs","byte_start":74267,"byte_end":74276,"line_start":1700,"line_end":1700,"column_start":17,"column_end":26,"is_primary":true,"text":[{"text":"        let mut nt_status: NTSTATUS = 0;","highlight_start":17,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"maybe it is overwritten before being read?","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: value assigned to `nt_status` is never read\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\core\\syscalls.rs:1700:17\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m1700\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let mut nt_status: NTSTATUS = 0;\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: maybe it is overwritten before being read?\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unnecessary `unsafe` block","code":{"code":"unused_unsafe","explanation":null},"level":"warning","spans":[{"file_name":"src\\core\\syscalls.rs","byte_start":1895,"byte_end":1901,"line_start":42,"line_end":42,"column_start":33,"column_end":39,"is_primary":true,"text":[{"text":"                let field_ptr = unsafe { core::ptr::addr_of!((*base_ptr).$field) };","highlight_start":33,"highlight_end":39}],"label":"unnecessary `unsafe` block","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\core\\syscalls.rs","byte_start":85250,"byte_end":85444,"line_start":2017,"line_end":2021,"column_start":41,"column_end":26,"is_primary":false,"text":[{"text":"                        let ldr_entry = container_of!(","highlight_start":41,"highlight_end":55},{"text":"                            first_module_entry_ptr,","highlight_start":1,"highlight_end":52},{"text":"                            WS_LDR_DATA_TABLE_ENTRY,","highlight_start":1,"highlight_end":53},{"text":"                            InLoadOrderLinks","highlight_start":1,"highlight_end":45},{"text":"                        );","highlight_start":1,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"container_of!","def_site_span":{"file_name":"src\\core\\syscalls.rs","byte_start":1420,"byte_end":1445,"line_start":33,"line_end":33,"column_start":1,"column_end":26,"is_primary":false,"text":[{"text":"macro_rules! container_of {","highlight_start":1,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},{"file_name":"src\\core\\syscalls.rs","byte_start":83994,"byte_end":84000,"line_start":1995,"line_end":1995,"column_start":13,"column_end":19,"is_primary":false,"text":[{"text":"            unsafe {","highlight_start":13,"highlight_end":19}],"label":"because it's nested under this `unsafe` block","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unnecessary `unsafe` block\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\core\\syscalls.rs:42:33\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m42\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m                let field_ptr = unsafe { core::ptr::addr_of!((*base_ptr).$field) };\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11munnecessary `unsafe` block\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m1995\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m            unsafe {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m               \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mbecause it's nested under this `unsafe` block\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m2017\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m                        let ldr_entry = container_of!(\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m _________________________________________-\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m2018\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                            first_module_entry_ptr,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m2019\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                            WS_LDR_DATA_TABLE_ENTRY,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m2020\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                            InLoadOrderLinks\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m2021\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                        );\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|_________________________-\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14min this macro invocation\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: this warning originates in the macro `container_of` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"value assigned to `main_fiber` is never read","code":{"code":"unused_assignments","explanation":null},"level":"warning","spans":[{"file_name":"src\\core\\execution.rs","byte_start":35055,"byte_end":35065,"line_start":806,"line_end":806,"column_start":17,"column_end":27,"is_primary":true,"text":[{"text":"        let mut main_fiber: LPVOID = ptr::null_mut();","highlight_start":17,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"maybe it is overwritten before being read?","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: value assigned to `main_fiber` is never read\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\core\\execution.rs:806:17\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m806\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let mut main_fiber: LPVOID = ptr::null_mut();\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: maybe it is overwritten before being read?\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"value assigned to `shellcode_fiber` is never read","code":{"code":"unused_assignments","explanation":null},"level":"warning","spans":[{"file_name":"src\\core\\execution.rs","byte_start":35110,"byte_end":35125,"line_start":807,"line_end":807,"column_start":17,"column_end":32,"is_primary":true,"text":[{"text":"        let mut shellcode_fiber: LPVOID = ptr::null_mut();","highlight_start":17,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"maybe it is overwritten before being read?","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: value assigned to `shellcode_fiber` is never read\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\core\\execution.rs:807:17\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m807\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let mut shellcode_fiber: LPVOID = ptr::null_mut();\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: maybe it is overwritten before being read?\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"value assigned to `cleanup_needed` is never read","code":{"code":"unused_assignments","explanation":null},"level":"warning","spans":[{"file_name":"src\\core\\execution.rs","byte_start":35170,"byte_end":35184,"line_start":808,"line_end":808,"column_start":17,"column_end":31,"is_primary":true,"text":[{"text":"        let mut cleanup_needed = false;","highlight_start":17,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"maybe it is overwritten before being read?","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: value assigned to `cleanup_needed` is never read\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\core\\execution.rs:808:17\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m808\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let mut cleanup_needed = false;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: maybe it is overwritten before being read?\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variable does not need to be mutable","code":{"code":"unused_mut","explanation":null},"level":"warning","spans":[{"file_name":"src\\core\\crypto.rs","byte_start":16311,"byte_end":16324,"line_start":376,"line_end":376,"column_start":13,"column_end":26,"is_primary":true,"text":[{"text":"        let mut encryptor = Aes128CbcEnc::new_from_slices(&key, &iv).unwrap();","highlight_start":13,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove this `mut`","code":null,"level":"help","spans":[{"file_name":"src\\core\\crypto.rs","byte_start":16311,"byte_end":16315,"line_start":376,"line_end":376,"column_start":13,"column_end":17,"is_primary":true,"text":[{"text":"        let mut encryptor = Aes128CbcEnc::new_from_slices(&key, &iv).unwrap();","highlight_start":13,"highlight_end":17}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: variable does not need to be mutable\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\core\\crypto.rs:376:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m376\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let mut encryptor = Aes128CbcEnc::new_from_slices(&key, &iv).unwrap();\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m----\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mhelp: remove this `mut`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variable does not need to be mutable","code":{"code":"unused_mut","explanation":null},"level":"warning","spans":[{"file_name":"src\\core\\crypto.rs","byte_start":21887,"byte_end":21900,"line_start":504,"line_end":504,"column_start":13,"column_end":26,"is_primary":true,"text":[{"text":"        let mut encryptor = Aes128CbcEnc::new_from_slices(&correct_key_bytes, &iv_bytes).unwrap();","highlight_start":13,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove this `mut`","code":null,"level":"help","spans":[{"file_name":"src\\core\\crypto.rs","byte_start":21887,"byte_end":21891,"line_start":504,"line_end":504,"column_start":13,"column_end":17,"is_primary":true,"text":[{"text":"        let mut encryptor = Aes128CbcEnc::new_from_slices(&correct_key_bytes, &iv_bytes).unwrap();","highlight_start":13,"highlight_end":17}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: variable does not need to be mutable\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\core\\crypto.rs:504:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m504\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let mut encryptor = Aes128CbcEnc::new_from_slices(&correct_key_bytes, &iv_bytes).unwrap();\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m----\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mhelp: remove this `mut`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `temp_config_key`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\core\\encrypted_memory.rs","byte_start":7663,"byte_end":7678,"line_start":195,"line_end":195,"column_start":13,"column_end":28,"is_primary":true,"text":[{"text":"        let temp_config_key = b\"THIS_IS_A_TEMP_CONFIG_KEY_123!\"; // 32 bytes for AES-256","highlight_start":13,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_variables)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\core\\encrypted_memory.rs","byte_start":7663,"byte_end":7678,"line_start":195,"line_end":195,"column_start":13,"column_end":28,"is_primary":true,"text":[{"text":"        let temp_config_key = b\"THIS_IS_A_TEMP_CONFIG_KEY_123!\"; // 32 bytes for AES-256","highlight_start":13,"highlight_end":28}],"label":null,"suggested_replacement":"_temp_config_key","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `temp_config_key`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\core\\encrypted_memory.rs:195:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m195\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let temp_config_key = b\"THIS_IS_A_TEMP_CONFIG_KEY_123!\"; // 32 bytes for AES-256\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_temp_config_key`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_variables)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `temp_config_iv`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\core\\encrypted_memory.rs","byte_start":7753,"byte_end":7767,"line_start":196,"line_end":196,"column_start":13,"column_end":27,"is_primary":true,"text":[{"text":"        let temp_config_iv = b\"TEMP_CONFIG_IV_!\"; // 16 bytes","highlight_start":13,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\core\\encrypted_memory.rs","byte_start":7753,"byte_end":7767,"line_start":196,"line_end":196,"column_start":13,"column_end":27,"is_primary":true,"text":[{"text":"        let temp_config_iv = b\"TEMP_CONFIG_IV_!\"; // 16 bytes","highlight_start":13,"highlight_end":27}],"label":null,"suggested_replacement":"_temp_config_iv","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `temp_config_iv`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\core\\encrypted_memory.rs:196:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m196\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let temp_config_iv = b\"TEMP_CONFIG_IV_!\"; // 16 bytes\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_temp_config_iv`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `target_name_wide`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\defense\\ppid.rs","byte_start":2231,"byte_end":2247,"line_start":51,"line_end":51,"column_start":9,"column_end":25,"is_primary":true,"text":[{"text":"    let target_name_wide: Vec<u16> = target_name_os.encode_wide().collect();","highlight_start":9,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\defense\\ppid.rs","byte_start":2231,"byte_end":2247,"line_start":51,"line_end":51,"column_start":9,"column_end":25,"is_primary":true,"text":[{"text":"    let target_name_wide: Vec<u16> = target_name_os.encode_wide().collect();","highlight_start":9,"highlight_end":25}],"label":null,"suggested_replacement":"_target_name_wide","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `target_name_wide`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\defense\\ppid.rs:51:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m51\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let target_name_wide: Vec<u16> = target_name_os.encode_wide().collect();\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_target_name_wide`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unnecessary `unsafe` block","code":{"code":"unused_unsafe","explanation":null},"level":"warning","spans":[{"file_name":"src\\defense\\sandbox.rs","byte_start":7513,"byte_end":7519,"line_start":230,"line_end":230,"column_start":30,"column_end":36,"is_primary":true,"text":[{"text":"        let num_processors = unsafe { system_info.dwNumberOfProcessors };","highlight_start":30,"highlight_end":36}],"label":"unnecessary `unsafe` block","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unnecessary `unsafe` block\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\defense\\sandbox.rs:230:30\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m230\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let num_processors = unsafe { system_info.dwNumberOfProcessors };\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11munnecessary `unsafe` block\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"value assigned to `clean_dll_base` is never read","code":{"code":"unused_assignments","explanation":null},"level":"warning","spans":[{"file_name":"src\\defense\\sandbox.rs","byte_start":42585,"byte_end":42599,"line_start":1063,"line_end":1063,"column_start":21,"column_end":35,"is_primary":true,"text":[{"text":"            let mut clean_dll_base: *mut c_void = ptr::null_mut();","highlight_start":21,"highlight_end":35}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"maybe it is overwritten before being read?","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: value assigned to `clean_dll_base` is never read\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\defense\\sandbox.rs:1063:21\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m1063\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            let mut clean_dll_base: *mut c_void = ptr::null_mut();\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: maybe it is overwritten before being read?\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"value assigned to `file_handle` is never read","code":{"code":"unused_assignments","explanation":null},"level":"warning","spans":[{"file_name":"src\\defense\\sandbox.rs","byte_start":42653,"byte_end":42664,"line_start":1064,"line_end":1064,"column_start":21,"column_end":32,"is_primary":true,"text":[{"text":"            let mut file_handle: HANDLE = windows_sys::Win32::Foundation::INVALID_HANDLE_VALUE;","highlight_start":21,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"maybe it is overwritten before being read?","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: value assigned to `file_handle` is never read\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\defense\\sandbox.rs:1064:21\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m1064\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            let mut file_handle: HANDLE = windows_sys::Win32::Foundation::INVALID_HANDLE_VALUE;\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: maybe it is overwritten before being read?\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"value assigned to `mapping_handle` is never read","code":{"code":"unused_assignments","explanation":null},"level":"warning","spans":[{"file_name":"src\\defense\\sandbox.rs","byte_start":42750,"byte_end":42764,"line_start":1065,"line_end":1065,"column_start":21,"column_end":35,"is_primary":true,"text":[{"text":"            let mut mapping_handle: HANDLE = std::ptr::null_mut(); // For CreateFileMapping, NULL is 0","highlight_start":21,"highlight_end":35}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"maybe it is overwritten before being read?","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: value assigned to `mapping_handle` is never read\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\defense\\sandbox.rs:1065:21\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m1065\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            let mut mapping_handle: HANDLE = std::ptr::null_mut(); // For CreateFileMapping, NULL is 0\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: maybe it is overwritten before being read?\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `syscall_manager`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\defense\\evasion_manager.rs","byte_start":2122,"byte_end":2137,"line_start":72,"line_end":72,"column_start":9,"column_end":24,"is_primary":true,"text":[{"text":"        syscall_manager: Option<Arc<Mutex<SyscallManager>>>,","highlight_start":9,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\defense\\evasion_manager.rs","byte_start":2122,"byte_end":2137,"line_start":72,"line_end":72,"column_start":9,"column_end":24,"is_primary":true,"text":[{"text":"        syscall_manager: Option<Arc<Mutex<SyscallManager>>>,","highlight_start":9,"highlight_end":24}],"label":null,"suggested_replacement":"_syscall_manager","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `syscall_manager`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\defense\\evasion_manager.rs:72:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m72\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        syscall_manager: Option<Arc<Mutex<SyscallManager>>>,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_syscall_manager`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"aborting due to 2 previous errors; 43 warnings emitted","code":null,"level":"error","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: aborting due to 2 previous errors; 43 warnings emitted\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"For more information about this error, try `rustc --explain E0432`.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;15mFor more information about this error, try `rustc --explain E0432`.\u001b[0m\n"}
