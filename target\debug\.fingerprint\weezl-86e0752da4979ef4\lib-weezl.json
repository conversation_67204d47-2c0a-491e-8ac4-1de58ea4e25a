{"rustc": 8024708092284749966, "features": "[\"alloc\", \"default\", \"std\"]", "declared_features": "[\"alloc\", \"async\", \"default\", \"futures\", \"std\"]", "target": 8369499057004385739, "profile": 2241668132362809309, "path": 9721325354290485617, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\weezl-86e0752da4979ef4\\dep-lib-weezl", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}