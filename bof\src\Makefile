# Makefile for BOF (Beacon Object File) Compilation
# 
# This Makefile compiles C source files into BOF object files
# that can be loaded and executed by Cobalt Strike Beacon.

# Compiler settings
CC = x86_64-w64-mingw32-gcc
CFLAGS = -c -Os -fno-asynchronous-unwind-tables -fno-ident -fpack-struct=8 -falign-functions=1 -s -ffunction-sections -falign-jumps=1 -w -Wall -shared -Wl,--subsystem,console
LDFLAGS = 

# Source and output directories
SRC_DIR = .
BIN_DIR = ../bin

# Source files
SOURCES = screenshot_bof.c
OBJECTS = $(SOURCES:.c=.o)
TARGET = $(BIN_DIR)/screenshot_bof.o

# Default target
all: $(TARGET)

# Create bin directory if it doesn't exist
$(BIN_DIR):
	mkdir -p $(BIN_DIR)

# Compile BOF
$(TARGET): $(OBJECTS) | $(BIN_DIR)
	@echo "Building BOF: $@"
	$(CC) $(CFLAGS) $(SRC_DIR)/screenshot_bof.c -o $@
	@echo "BOF compilation completed: $@"

# Compile individual object files
%.o: %.c
	@echo "Compiling: $<"
	$(CC) $(CFLAGS) $< -o $@

# Clean build artifacts
clean:
	@echo "Cleaning build artifacts..."
	rm -f $(SRC_DIR)/*.o
	rm -f $(TARGET)
	@echo "Clean completed"

# Install BOF to CS aggressor scripts directory (optional)
install: $(TARGET)
	@echo "Installing BOF..."
	@if [ -n "$(CS_SCRIPTS_DIR)" ]; then \
		cp $(TARGET) $(CS_SCRIPTS_DIR)/; \
		echo "BOF installed to $(CS_SCRIPTS_DIR)"; \
	else \
		echo "CS_SCRIPTS_DIR not set. Please set it to your Cobalt Strike scripts directory"; \
	fi

# Test compilation (dry run)
test:
	@echo "Testing BOF compilation..."
	$(CC) $(CFLAGS) -fsyntax-only $(SRC_DIR)/screenshot_bof.c
	@echo "Syntax check passed"

# Show help
help:
	@echo "Available targets:"
	@echo "  all      - Build the BOF (default)"
	@echo "  clean    - Remove build artifacts"
	@echo "  test     - Test compilation (syntax check only)"
	@echo "  install  - Install BOF to CS scripts directory"
	@echo "  help     - Show this help message"
	@echo ""
	@echo "Environment variables:"
	@echo "  CS_SCRIPTS_DIR - Cobalt Strike scripts directory for installation"
	@echo ""
	@echo "Example usage:"
	@echo "  make                    # Build BOF"
	@echo "  make clean              # Clean build"
	@echo "  make CS_SCRIPTS_DIR=/path/to/cs/scripts install"

# Phony targets
.PHONY: all clean test install help
